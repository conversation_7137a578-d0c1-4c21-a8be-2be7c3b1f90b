from flask import Flask, request, jsonify
import threading

class APIServer:
    def __init__(self, order_helper, gui_update_callback=None):
        self.app = Flask(__name__)
        self.order_helper = order_helper
        self.gui_update_callback = gui_update_callback
        self.server = None
        self.is_running = False
        
        # 註冊路由
        @self.app.route('/place_order', methods=['POST'])
        def place_order():
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"success": False, "message": "無效的請求數據"}), 400
                
                # 提取參數
                product_name = data.get('product_name', '')
                order_type = data.get('order_type', 'Market')
                quantity = int(data.get('quantity', 1))
                sl = data.get('sl', '0')
                tp = data.get('tp', '0')
                price = data.get('price')
                test_mode = data.get('test_mode', True)
                direction = data.get('direction', 'Buy')
                
                # 更新GUI輸入框
                if self.gui_update_callback:
                    self.gui_update_callback(
                        product_name=product_name,
                        order_type=order_type,
                        quantity=quantity,
                        sl=sl,
                        tp=tp,
                        price=price,
                        direction=direction
                    )
                
                # 執行下單
                success, message = self.order_helper.place_order(
                    product_name=product_name,
                    order_type=order_type,
                    quantity=quantity,
                    sl=sl,
                    tp=tp,
                    price=price,
                    test_mode=test_mode,
                    direction=direction
                )
                
                return jsonify({
                    "success": success,
                    "message": message
                })
                
            except Exception as e:
                return jsonify({
                    "success": False,
                    "message": f"處理請求時發生錯誤: {str(e)}"
                }), 500
    
    def start(self):
        """啟動API服務器"""
        if not self.is_running:
            def run_server():
                self.app.run(host='0.0.0.0', port=8168)
            
            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()
            self.is_running = True
            return True
        return False
    
    def stop(self):
        """停止API服務器"""
        if self.is_running:
            # 關閉Flask服務器
            try:
                import requests
                requests.get(f'http://localhost:8168/shutdown')
            except:
                pass
            self.is_running = False
            return True
        return False
    
    def set_order_helper(self, order_helper):
        """設置OrderHelper實例"""
        self.order_helper = order_helper 