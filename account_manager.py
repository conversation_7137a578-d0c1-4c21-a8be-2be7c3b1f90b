import tkinter as tk
from tkinter import messagebox
from login import LoginHelper

class AccountManagerGUI:
    def __init__(self):
        self.login_helper = LoginHelper()
        self.root = tk.Tk()
        self.root.title("帳號密碼加密保存")
        self.root.geometry("350x180")

        tk.Label(self.root, text="帳號 (Username):").pack(pady=(10, 0))
        self.username_entry = tk.Entry(self.root, width=30)
        self.username_entry.pack()

        tk.Label(self.root, text="密碼 (Password):").pack(pady=(10, 0))
        self.password_entry = tk.Entry(self.root, show="*", width=30)
        self.password_entry.pack()

        tk.Button(self.root, text="保存加密", command=self.save_credentials).pack(pady=15)

    def save_credentials(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        if not username or not password:
            messagebox.showerror("錯誤", "帳號和密碼不能為空！")
            return
        try:
            # 這裡假設 driver 已啟動且有 current_url，否則用一個預設url
            if not self.login_helper.driver:
                self.login_helper.start_browser("https://topstepx.com/login")
            self.login_helper.encrypt_credentials(username, password)
            messagebox.showinfo("成功", "帳號密碼已加密保存！")
        except Exception as e:
            messagebox.showerror("錯誤", f"保存失敗: {str(e)}")

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    gui = AccountManagerGUI()
    gui.run() 