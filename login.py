from selenium import webdriver
from selenium.webdriver.firefox.service import Service
from webdriver_manager.firefox import GeckoDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
from cryptography.fernet import Fernet
import json
import os
from seleniumhelper import SeleniumHelper

class LoginHelper:
    def __init__(self):
        self.driver = None
        self.setup_encryption()
        self._status_callback = None
        self._detection_callback = None
        self._url_callback = None
        self.selenium_helper = SeleniumHelper()
        
    def set_callbacks(self, status_callback=None, detection_callback=None, url_callback=None):
        """設置回調函數"""
        self._status_callback = status_callback
        self._detection_callback = detection_callback
        self._url_callback = url_callback
        
    def _update_status(self, message):
        """更新狀態信息"""
        if self._status_callback:
            self._status_callback(message)
            
    def _update_detection(self, message, color="black"):
        """更新檢測狀態"""
        if self._detection_callback:
            self._detection_callback(message, color)
            
    def _update_url(self, url):
        """更新URL顯示"""
        if self._url_callback:
            self._url_callback(url)

    def monitor_status(self):
        """監控登入狀態"""
        try:
            current_url = self.get_current_url()
            if current_url:
                if "topstepx.com/trade" in current_url:
                    if self.check_logout_button():
                        self._update_detection("已登入", "green")
                    else:
                        self._update_detection("登入狀態已失效", "red")
                else:
                    self._update_detection("非交易頁面", "orange")
                self._update_url(current_url)
            else:
                self._update_detection("無法連接瀏覽器", "red")
                self._update_url("無法獲取當前URL")
        except Exception as e:
            self._update_detection("檢測錯誤", "orange")
            print(f"Monitoring error: {str(e)}")

    def setup_encryption(self):
        """設置加密相關的配置"""
        self.key_file = "encryption.key"
        self.creds_file = "credentials.enc"
        
        # 如果key不存在，生成新的key
        if not os.path.exists(self.key_file):
            key = Fernet.generate_key()
            with open(self.key_file, "wb") as f:
                f.write(key)
        
        # 讀取key
        with open(self.key_file, "rb") as f:
            self.key = f.read()
        self.cipher_suite = Fernet(self.key)

    def start_browser(self, url="https://topstepx.com/login"):
        """啟動瀏覽器並訪問指定URL"""
        try:
            if not self.driver:
                self._update_status("Starting browser...")
                service = Service(GeckoDriverManager().install())
                options = webdriver.FirefoxOptions()
                options.set_preference("dom.webdriver.enabled", False)
                options.set_preference('useAutomationExtension', False)
                self.driver = webdriver.Firefox(service=service, options=options)
                self.selenium_helper.set_driver(self.driver)  # 設置 SeleniumHelper 的 driver
                self.driver.get(url)
                self._update_status("Browser launched successfully")
                return True
            return False
        except Exception as e:
            self._update_status(f"Failed to start browser: {str(e)}")
            return False

    def check_login_element(self):
        """檢查登入頁面元素是否存在"""
        try:
            # 檢查用戶名輸入框
            username_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                "input.MuiInputBase-input[name='userName'][type='text']")
            
            # 檢查密碼輸入框
            password_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                "input.MuiInputBase-input[name='password'][type='password']")
            
            # 檢查登入按鈕
            signin_buttons = self.driver.find_elements(By.CSS_SELECTOR, 
                "button.MuiButtonBase-root[type='submit']")
            
            # 輸出檢測結果
            print(f"Found elements - Username: {len(username_elements)}, Password: {len(password_elements)}, Buttons: {len(signin_buttons)}")
            
            if len(signin_buttons) > 0:
                for button in signin_buttons:
                    button_text = button.text.strip().lower()
                    print(f"Button text: '{button_text}'")
            
            # 檢查是否有符合條件的登入按鈕
            has_signin_button = False
            for button in signin_buttons:
                button_text = button.text.strip().lower()
                if "sign in" in button_text or "signin" in button_text:
                    has_signin_button = True
                    break
            
            # 所有元素都必須存在
            elements_exist = (len(username_elements) > 0 and 
                            len(password_elements) > 0 and 
                            has_signin_button)
            
            print(f"Login elements check result: {elements_exist}")
            return elements_exist
            
        except Exception as e:
            print(f"Error checking login elements: {str(e)}")
            return False

    def check_logout_button(self):
        """檢查登出按鈕是否存在（用於確認登入成功）"""
        try:
            self.driver.find_element(By.CSS_SELECTOR, 
                "img.MuiBox-root[aria-label='Log Out']")
            return True
        except Exception:
            return False

    def encrypt_credentials(self, username, password):
        """加密並保存憑證"""
        credentials = {
            "url": self.driver.current_url,
            "username": username,
            "password": password
        }
        encrypted_data = self.cipher_suite.encrypt(json.dumps(credentials).encode())
        with open(self.creds_file, "wb") as f:
            f.write(encrypted_data)

    def decrypt_credentials(self):
        """解密並讀取憑證"""
        try:
            if os.path.exists(self.creds_file):
                with open(self.creds_file, "rb") as f:
                    encrypted_data = f.read()
                decrypted_data = self.cipher_suite.decrypt(encrypted_data)
                return json.loads(decrypted_data.decode())
            return None
        except Exception as e:
            print(f"Error decrypting credentials: {str(e)}")
            return None

    def autofill_credentials(self):
        """自動填充已保存的憑證"""
        try:
            credentials = self.decrypt_credentials()
            if credentials and credentials["url"] == self.driver.current_url:
                # 填充用戶名
                username_input = self.driver.find_element(By.CSS_SELECTOR, 
                    "input.MuiInputBase-input[name='userName'][type='text']")
                username_input.clear()
                username_input.send_keys(credentials["username"])
                
                # 填充密碼
                password_input = self.driver.find_element(By.CSS_SELECTOR, 
                    "input.MuiInputBase-input[name='password'][type='password']")
                password_input.clear()
                password_input.send_keys(credentials["password"])
                return True
        except Exception as e:
            print(f"Error auto-filling credentials: {str(e)}")
        return False

    def perform_login(self, retry_count=0, max_retries=3):
        """執行登入操作"""
        try:
            if retry_count >= max_retries:
                self._update_status(f"已達到最大重試次數 ({max_retries})")
                return False

            # 首先嘗試自動填充
            credentials_filled = self.autofill_credentials()
            
            if not credentials_filled:
                try:
                    # 如果沒有自動填充，獲取當前輸入框的值
                    username_input = self.driver.find_element(By.CSS_SELECTOR, 
                        "input.MuiInputBase-input[name='userName'][type='text']")
                    password_input = self.driver.find_element(By.CSS_SELECTOR, 
                        "input.MuiInputBase-input[name='password'][type='password']")
                    
                    username = username_input.get_attribute('value')
                    password = password_input.get_attribute('value')
                    
                    # 如果有輸入值，保存新的憑證
                    if username and password:
                        self.encrypt_credentials(username, password)
                except Exception as e:
                    print(f"Error finding input fields: {str(e)}")
                    self._update_status("找不到輸入框，刷新頁面重試...")
                    self.driver.refresh()
                    time.sleep(2)  # 等待頁面刷新
                    return self.perform_login(retry_count + 1, max_retries)
            
            # 記錄點擊前的URL
            current_url = self.driver.current_url
            
            try:
                # 尋找並點擊登入按鈕
                signin_button = self.driver.find_element(By.CSS_SELECTOR, 
                    "button.MuiButtonBase-root[type='submit']")
                button_text = signin_button.text.strip().lower()
                
                if "sign in" in button_text or "signin" in button_text:
                    signin_button.click()
                    
                    # 等待3秒檢查URL是否有變化
                    time.sleep(3)
                    
                    # 如果URL沒有變化，刷新頁面並重試
                    if self.driver.current_url == current_url:
                        self._update_status(f"登入頁面沒有響應，刷新重試... (第 {retry_count + 1} 次)")
                        self.driver.refresh()
                        time.sleep(2)  # 等待頁面刷新
                        return self.perform_login(retry_count + 1, max_retries)
                    
                    return True
                else:
                    self._update_status("找不到正確的登入按鈕，刷新重試...")
                    self.driver.refresh()
                    time.sleep(2)
                    return self.perform_login(retry_count + 1, max_retries)
            except Exception as e:
                print(f"Error finding or clicking sign in button: {str(e)}")
                self._update_status("找不到登入按鈕，刷新重試...")
                self.driver.refresh()
                time.sleep(2)
                return self.perform_login(retry_count + 1, max_retries)
                
            return False
        except Exception as e:
            print(f"Error performing login: {str(e)}")
            return False

    def wait_for_login_success(self, timeout=30):
        """等待登入成功（檢測登出按鈕）"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            current_url = self.driver.current_url
            if "topstepx.com/trade" in current_url and self.check_logout_button():
                return True
            time.sleep(0.5)
        return False

    def wait_for_login_page(self):
        """等待登入頁面加載，包含重試邏輯"""
        retry_count = 0
        while True:  # 無限重試
            try:
                # 檢查用戶名輸入框
                username_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                    "input[name='userName'][type='text']")
                
                # 檢查密碼輸入框
                password_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                    "input[name='password'][type='password']")
                
                # 檢查登入按鈕
                signin_buttons = self.driver.find_elements(By.CSS_SELECTOR, 
                    "button[type='submit']")
                
                # 如果沒有找到任何輸入框或按鈕，立即刷新頁面
                if len(username_elements) == 0 or len(password_elements) == 0 or len(signin_buttons) == 0:
                    self._update_status(f"找不到登入元素，刷新頁面... (重試次數: {retry_count + 1})")
                    print(f"找不到登入元素，刷新頁面... (重試次數: {retry_count + 1})")
                    print(f"用戶名輸入框: {len(username_elements)}, 密碼輸入框: {len(password_elements)}, 登入按鈕: {len(signin_buttons)}")
                    self.driver.refresh()
                    time.sleep(2)  # 等待頁面刷新
                    retry_count += 1
                    continue
                
                # 檢查是否有符合條件的登入按鈕
                has_signin_button = False
                for button in signin_buttons:
                    button_text = button.text.strip().lower()
                    if "sign in" in button_text or "signin" in button_text:
                        has_signin_button = True
                        break
                
                if has_signin_button:
                    self._update_status("已找到登入頁面")
                    return True
                else:
                    # 如果找不到正確的登入按鈕，也需要刷新
                    self._update_status(f"找不到正確的登入按鈕，刷新頁面... (重試次數: {retry_count + 1})")
                    self.driver.refresh()
                    time.sleep(2)
                    retry_count += 1
                    continue
                
            except Exception as e:
                print(f"檢查登入頁面時發生錯誤: {str(e)}")
                self._update_status(f"檢查登入頁面時發生錯誤，刷新頁面... (重試次數: {retry_count + 1})")
                self.driver.refresh()
                time.sleep(2)
                retry_count += 1
                continue
            
            retry_count += 1
            retry_interval = min(retry_count, 10)  # 最大等待10秒
            self._update_status(f"等待登入頁面... (重試次數: {retry_count}, {retry_interval} 秒後重試)")
            
            # 每5次重試就刷新一次頁面
            if retry_count % 5 == 0:
                self.driver.refresh()
                time.sleep(2)  # 刷新後多等待2秒
            else:
                time.sleep(retry_interval)

    def auto_login(self):
        """執行完整的自動登入流程"""
        try:
            max_retries = 3  # 最大重試次數
            retry_count = 0
            
            while retry_count < max_retries:
                # 等待登入頁面
                if self.wait_for_login_page():
                    # 執行登入
                    self._update_status("Attempting login...")
                    if self.perform_login():
                        self._update_status("Login initiated, waiting for confirmation...")
                        # 等待登入成功
                        if self.wait_for_login_success():
                            self._update_status("Login successful")
                            return True
                        else:
                            retry_count += 1
                            self._update_status(f"Login timeout (Attempt {retry_count}/{max_retries})")
                            if retry_count < max_retries:
                                self._update_status("Refreshing page and retrying...")
                                self.driver.refresh()
                                time.sleep(2)  # 等待頁面刷新
                                # 重新載入緩存的憑證
                                self.autofill_credentials()
                                continue
                    else:
                        self._update_status("Login failed")
                else:
                    self._update_status("Could not find login page")
                break  # 如果不是超時問題，直接退出循環
            
            return False
        except Exception as e:
            self._update_status(f"Error during auto-login: {str(e)}")
            return False

    def get_current_url(self):
        """獲取當前URL"""
        try:
            return self.driver.current_url if self.driver else None
        except Exception:
            return None

    def close(self):
        """關閉瀏覽器"""
        if self.driver:
            self.driver.quit()
            self.driver = None 

    def perform_logout(self):
        """執行登出操作"""
        try:
            self._update_status("正在登出...")
            
            # 使用 SeleniumHelper 獲取登出按鈕
            logout_button, error = self.selenium_helper.get_operation_button('Log Out')
            if not logout_button:
                self._update_status(error)
                return False
            
            # 點擊登出按鈕
            logout_button.click()
            
            # 等待URL變化或登入按鈕出現
            start_time = time.time()
            while time.time() - start_time < 10:  # 最多等待10秒
                if "login" in self.driver.current_url.lower():
                    self._update_status("已成功登出")
                    return True
                time.sleep(0.5)
            
            self._update_status("登出超時")
            return False
            
        except Exception as e:
            self._update_status(f"登出失敗: {str(e)}")
            print(f"Error during logout: {str(e)}")
            return False 