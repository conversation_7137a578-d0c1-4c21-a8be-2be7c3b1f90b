# XPlatformHelper

一個自動化的登入監控工具，用於監控網頁登入狀態。

## 功能特點

- 自動登入網頁
- 監控登入狀態
- 自動保存和填充登入憑證
- 即時狀態顯示
- 安全的憑證加密存儲

## 安裝需求

```bash
pip install -r requirements.txt
```

## 使用方法

1. 運行程序：
```bash
python main.py
```

2. 程序會自動：
   - 啟動瀏覽器
   - 訪問登入頁面
   - 自動填充已保存的憑證（如果有）
   - 監控登入狀態

## 注意事項

- 首次運行時需要手動輸入登入憑證
- 憑證會被加密保存在本地
- 請確保 Firefox 瀏覽器已安裝

## 環境要求

- Python 3.6+
- 虛擬環境 (venv)
- Firefox瀏覽器 (用於Selenium)

## 安裝步驟

1. 克隆此倉庫
2. 創建並激活虛擬環境：
   ```bash
   python -m venv venv
   .\venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   ```
3. 安裝依賴：
   ```bash
   pip install -r requirements.txt
   ```

## 貢獻指南

歡迎提交 Pull Requests 來改進這個項目。

## 許可證

MIT License 