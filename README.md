# XPlatformHelper

一個支持 Bracket Order 功能的自動化交易助手，提供 GUI 界面和 REST API。

## 主要功能

### 🎯 双模式交易系统

#### 1. 普通模式 (Account Risk)
- **SL/TP 含义**: 金額 (红色标签)
- **工作方式**: 在 Settings → Risk Settings 设定账户级别的止损止盈
- **适用场景**: 传统的账户风险管理
- **限制**: 同一时间同一账户只能对一个商品生效

#### 2. Bracket Order 模式 (独立订单)
- **SL/TP 含义**: 價格 (蓝色标签)
- **工作方式**:
  - 自动获取当前市场价格并验证止损止盈设置
  - 跳过 Settings，分三步骤下独立订单
  1. 主要订单 (用户指定的方向和类型)
  2. 止损订单 (相反方向的 Stop Market)
  3. 止盈订单 (相反方向的 Limit)
- **价格验证**:
  - 做多: 止损 < 当前价格 < 止盈
  - 做空: 止盈 < 当前价格 < 止损
- **适用场景**: 每笔交易独立的风险管理
- **优势**: 可同时对多个商品使用不同的止损止盈价格

### 📊 GUI 界面特性

- **智能标签**: 根据模式自动切换 SL/TP 标签颜色和说明
- **实时状态**: 显示当前使用的交易模式
- **测试模式**: 安全的订单测试功能
- **一键切换**: 简单的复选框切换两种模式

### 🔌 REST API

- **端口**: 8168
- **格式**: JSON
- **功能**: 完整的下单功能，支持两种模式
- **文档**: 详细的 API 文档和示例

## 安裝需求

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 启动程序
```bash
python main.py
```

### 2. 登录交易平台
- 程序会自动打开浏览器
- 完成登录后，界面会显示"已登入"状态

### 3. 选择交易模式

#### 普通模式
- 不勾选 "启用Bracket Order"
- SL/TP 标签显示为红色 "SL(金額)" "TP(金額)"
- 系统会到 Settings 设定 Account Risk

#### Bracket Order 模式
- 勾选 "启用Bracket Order"
- SL/TP 标签显示为蓝色 "SL(價格)" "TP(價格)"
- 系统会跳过 Settings，直接下独立订单

### 4. 下单
- 填写商品名称、方向、数量等参数
- 设定 SL/TP 值 (注意金額 vs 價格的区别)
- 点击"下單"按钮

## API 使用

### 启动 API 服务器
1. 在 GUI 中点击 "启动API" 按钮
2. 确认状态显示 "API服务器: 已启动"

### API 调用示例

#### 普通模式 (金額)
```python
import requests

data = {
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "quantity": 1,
    "sldollars": "1000", # 金額
    "tpdollars": "2000", # 金額
    "test_mode": False,
    "bracket_order": False
}

response = requests.post("http://localhost:8168/place_order", json=data)
```

#### Bracket Order 模式 (價格)
```python
data = {
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "quantity": 1,
    "sl": "45000",       # 價格
    "tp": "55000",       # 價格
    "test_mode": False,
    "bracket_order": True
}

response = requests.post("http://localhost:8168/place_order", json=data)
```

#### 自動模式選擇 (根據GUI狀態)
```python
data = {
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "quantity": 1,
    "sl": "45000",       # GUI勾選時使用(價格)
    "tp": "55000",       # GUI勾選時使用(價格)
    "sldollars": "1000", # GUI未勾選時使用(金額)
    "tpdollars": "2000", # GUI未勾選時使用(金額)
    "test_mode": False
    # 不指定bracket_order，系統自動根據GUI狀態選擇
}

response = requests.post("http://localhost:8168/place_order", json=data)
```

### 测试 API
```bash
python api_test_examples.py
```

## 文件说明

### 核心程序
- `main.py` - 主程序和 GUI 界面
- `order.py` - 下单逻辑和 Bracket Order 实现
- `api_server.py` - REST API 服务器
- `login.py` - 登录和浏览器控制

### 文档和测试
- `API_Documentation.md` - 详细的 API 文档
- `Parameter_Reference.md` - 参数对照表和迁移指南
- `Bracket_Order_Guide.md` - Bracket Order 详细使用指南
- `api_test_examples.py` - API 测试示例脚本

## 重要提醒

### ⚠️ SL/TP 参数区别

| 模式 | SL/TP 含义 | 标签颜色 | 设定位置 | 影响范围 |
|------|------------|----------|----------|----------|
| 普通模式 | 金額 | 🔴 红色 | Settings → Risk | 账户级别 |
| Bracket Order | 價格 | 🔵 蓝色 | 独立订单 | 单笔交易 |

### 🔒 安全建议

1. **首次使用**: 建议先用测试模式 (`test_mode: true`) 验证
2. **参数确认**: 仔细确认 SL/TP 是金額还是價格
3. **保证金**: Bracket Order 会下多个订单，确保账户有足够保证金
4. **网络稳定**: 确保网络连接稳定，避免订单执行中断

## 故障排除

### 常见问题

1. **API 连接失败**
   - 确认 XPlatformHelper 已启动
   - 确认已点击 "启动API" 按钮
   - 检查端口 8168 是否被占用

2. **下单失败**
   - 确认已登录交易平台
   - 检查商品名称是否正确
   - 确认账户有足够保证金

3. **SL/TP 设定错误**
   - 确认当前使用的模式 (普通 vs Bracket Order)
   - 检查输入的是金額还是價格
   - 查看 GUI 标签颜色提示

## 環境要求

- Python 3.6+
- 虛擬環境 (venv)
- Firefox瀏覽器 (用於Selenium)

## 安裝步驟

1. 克隆此倉庫
2. 創建並激活虛擬環境：
   ```bash
   python -m venv venv
   .\venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   ```
3. 安裝依賴：
   ```bash
   pip install -r requirements.txt
   ```

## 貢獻指南

歡迎提交 Pull Requests 來改進這個項目。

## 許可證

MIT License 