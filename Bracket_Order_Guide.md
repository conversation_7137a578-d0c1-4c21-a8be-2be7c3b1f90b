# Bracket Order 详细指南

## 概述

Bracket Order 是一种高级交易策略，它会自动下三个相关的订单来形成一个"包围"结构，为您的交易提供完整的风险管理。

## 工作原理

### 执行流程

当您启用 Bracket Order 模式时，系统会先进行价格验证，然后按以下顺序执行三个独立的下单操作：

#### 前置验证步骤
- **获取当前市场价格**: 系统自动获取当前市场价格
- **价格合理性检查**: 验证止损止盈价格是否符合交易逻辑
- **逻辑关系验证**: 确保价格设置的逻辑正确性

#### 三步骤下单流程

#### 步骤 1: 主要订单
- **目的**: 建立您的主要仓位
- **方向**: 您指定的交易方向 (Buy/Sell)
- **类型**: 您指定的订单类型 (Market/Limit/Stop Market)
- **价格**: 您指定的价格 (如果是限价单)

#### 步骤 2: 止损订单 (如果设置了 SL)
- **目的**: 限制您的最大损失
- **方向**: 与主要订单相反
- **类型**: **Stop Market** (固定)
- **价格**: SL 参数指定的价格
- **触发**: 当市场价格达到止损价格时自动执行

#### 步骤 3: 止盈订单 (如果设置了 TP)
- **目的**: 锁定您的目标利润
- **方向**: 与主要订单相反
- **类型**: **Limit** (固定)
- **价格**: TP 参数指定的价格
- **执行**: 当市场价格达到止盈价格时自动执行

## 实际示例

### 示例 1: 做多 BTCUSDT (Market Order)

**输入参数:**
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "order_type": "Market",
  "quantity": 1,
  "sl": "45000",
  "tp": "55000",
  "bracket_order": true
}
```

**执行步骤:**
1. **主单**: Buy 1 BTCUSDT Market (立即以市价买入)
2. **止损**: Sell 1 BTCUSDT Stop Market @ 45000 (如果价格跌到45000就止损)
3. **止盈**: Sell 1 BTCUSDT Limit @ 55000 (如果价格涨到55000就止盈)

**结果**: 您在当前市价买入了 BTCUSDT，同时设置了 45000 的止损和 55000 的止盈。

### 示例 2: 做空 ETHUSDT (Limit Order)

**输入参数:**
```json
{
  "product_name": "ETHUSDT",
  "direction": "Sell",
  "order_type": "Limit",
  "quantity": 2,
  "price": 3000,
  "sl": "3100",
  "tp": "2800",
  "bracket_order": true
}
```

**执行步骤:**
1. **主单**: Sell 2 ETHUSDT Limit @ 3000 (在3000价位挂卖单)
2. **止损**: Buy 2 ETHUSDT Stop Market @ 3100 (如果价格涨到3100就止损)
3. **止盈**: Buy 2 ETHUSDT Limit @ 2800 (如果价格跌到2800就止盈)

**结果**: 您在 3000 挂了卖单，同时设置了 3100 的止损和 2800 的止盈。

## 价格验证规则

### 做多 (Buy) 交易的价格要求

```
止损价格 < 当前市场价格 < 止盈价格
```

**逻辑说明**:
- **止损 (Stop Market)**: 当价格下跌到止损价格时，以市价卖出止损
- **止盈 (Limit)**: 当价格上涨到止盈价格时，以限价卖出获利

**示例** (当前价格 50000):
- ✅ 正确: SL=45000, TP=55000
- ❌ 错误: SL=55000 (止损不能高于当前价格)
- ❌ 错误: TP=45000 (止盈不能低于当前价格)

### 做空 (Sell) 交易的价格要求

```
止盈价格 < 当前市场价格 < 止损价格
```

**逻辑说明**:
- **止损 (Stop Market)**: 当价格上涨到止损价格时，以市价买入止损
- **止盈 (Limit)**: 当价格下跌到止盈价格时，以限价买入获利

**示例** (当前价格 3000):
- ✅ 正确: SL=3100, TP=2800
- ❌ 错误: SL=2900 (止损不能低于当前价格)
- ❌ 错误: TP=3200 (止盈不能高于当前价格)

### 常见价格设置错误

1. **止损止盈位置颠倒**
   ```
   做多时: SL=55000, TP=45000 (当前价格50000)
   错误: 止损应该在下方，止盈应该在上方
   ```

2. **止损设置在错误方向**
   ```
   做空时: SL=2900 (当前价格3000)
   错误: 做空止损应该在当前价格上方
   ```

3. **止盈设置在错误方向**
   ```
   做多时: TP=45000 (当前价格50000)
   错误: 做多止盈应该在当前价格上方
   ```

## 技术实现细节

### 每个步骤的操作

每个订单都是完全独立的操作，包括：

1. **设置订单类型**
   - 系统会重新选择订单类型 (Market/Limit/Stop Market)
   - 确保界面显示正确的订单类型

2. **设置价格** (如果需要)
   - 清空当前价格输入框
   - 输入新的价格
   - 确认价格设置

3. **执行下单**
   - 点击相应的买入/卖出按钮
   - 等待订单确认

4. **等待间隔**
   - 每个订单之间等待 2 秒
   - 确保前一个订单完全处理完毕

### 订单类型映射

| 订单用途 | 固定类型 | 说明 |
|----------|----------|------|
| 主要订单 | 用户指定 | Market/Limit/Stop Market 任选 |
| 止损订单 | Stop Market | 固定，确保快速执行 |
| 止盈订单 | Limit | 固定，确保在目标价格执行 |

## 优势与特点

### 1. 完整的风险管理
- 自动设置止损，限制最大损失
- 自动设置止盈，锁定目标利润
- 无需手动监控市场

### 2. 独立订单系统
- 每个订单都是独立的
- 不依赖账户级别的 Risk Settings
- 可以同时对多个商品使用

### 3. 灵活的主单类型
- 支持 Market Order (立即执行)
- 支持 Limit Order (指定价格)
- 支持 Stop Market Order (突破交易)

### 4. 精确的价格控制
- SL/TP 直接指定价格点
- 不是金额或百分比
- 更精确的风险控制

## 注意事项

### 1. 保证金要求
- Bracket Order 会下多个订单
- 确保账户有足够的保证金
- 建议预留额外的保证金缓冲

### 2. 网络稳定性
- 三个订单需要连续执行
- 确保网络连接稳定
- 避免在网络不稳定时使用

### 3. 价格设置
- SL 应该是合理的止损价格
- TP 应该是合理的止盈价格
- 确保价格符合市场逻辑

### 4. 市场条件
- 在高波动市场中要特别小心
- 考虑滑点对订单执行的影响
- 建议在流动性好的时段使用

## 故障排除

### 常见问题

1. **主要订单成功，但止损/止盈失败**
   - 检查账户保证金是否充足
   - 确认价格设置是否合理
   - 查看错误消息了解具体原因

2. **订单类型设置失败**
   - 确认交易平台界面正常
   - 检查网络连接
   - 重试操作

3. **价格设置失败**
   - 确认价格格式正确
   - 检查价格是否在合理范围内
   - 确认输入框可以正常操作

### 调试建议

1. **使用测试模式**
   - 首次使用时建议开启测试模式
   - 验证所有参数设置正确
   - 确认执行流程符合预期

2. **检查状态消息**
   - 关注每个步骤的状态更新
   - 查看详细的错误信息
   - 根据消息调整参数

3. **分步验证**
   - 可以先测试普通订单
   - 确认基础功能正常
   - 再尝试 Bracket Order

## 最佳实践

1. **合理设置止损止盈**
   - 止损不要设置得太紧
   - 止盈要有合理的风险回报比
   - 考虑市场波动性

2. **选择合适的时机**
   - 避免在重要新闻发布时使用
   - 选择流动性充足的时段
   - 确保市场相对稳定

3. **监控执行结果**
   - 检查所有三个订单是否成功
   - 确认订单参数正确
   - 必要时手动调整

4. **资金管理**
   - 不要使用全部可用保证金
   - 预留应急资金
   - 分散风险，不要全仓操作
