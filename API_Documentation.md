# XPlatformHelper API 文档

## 概述

XPlatformHelper 提供了一个 REST API 服务器，允许通过 HTTP 请求进行自动化交易操作。API 服务器运行在端口 8168 上。

## 启动 API 服务器

1. 运行 XPlatformHelper 主程序
2. 点击 "启动API" 按钮
3. 确认看到 "API服务器: 已启动" 状态

## API 端点

### POST /place_order

执行下单操作

**URL:** `http://localhost:8168/place_order`

**方法:** POST

**Content-Type:** application/json

#### 请求参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| product_name | string | 是 | - | 商品名称 (如: "BTCUSDT", "ETHUSDT") |
| direction | string | 是 | "Buy" | 交易方向: "Buy" 或 "Sell" |
| order_type | string | 否 | "Market" | 订单类型: "Market", "Limit", "Stop Market" |
| quantity | integer | 否 | 1 | 交易数量 (正整数) |
| sl | string | 否 | "0" | 止损值 |
| tp | string | 否 | "0" | 止盈值 |
| price | number | 否 | null | 价格 (Limit/Stop Market 订单必需) |
| test_mode | boolean | 否 | true | 是否为测试模式 |
| bracket_order | boolean | 否 | false | 是否启用 Bracket Order |

#### 重要说明

##### SL/TP 参数的含义

**根据 bracket_order 参数的不同，sl 和 tp 的含义完全不同：**

1. **普通模式 (bracket_order: false)**
   - `sl` 和 `tp` 表示 **金额**
   - 系统会到 Settings → Risk Settings 设定账户级别的止损止盈
   - 这个设定会影响该商品的所有后续订单
   - 同一时间同一账户只能对一个商品生效

2. **Bracket Order 模式 (bracket_order: true)**
   - `sl` 和 `tp` 表示 **价格**
   - 系统会跳过 Settings 设定，直接下多个独立订单
   - 每个 Bracket Order 都是独立的，不影响账户设定
   - 可以同时对多个商品使用不同的止损止盈价格

##### Bracket Order 下单逻辑

当 `bracket_order: true` 时：

1. **主要订单**: 按指定方向下单
2. **止损订单**: 如果 `sl != "0"`，下一个相反方向的订单
3. **止盈订单**: 如果 `tp != "0"`，下一个相反方向的订单

**示例:**
- 主单: Buy BTCUSDT
- 止损单: Sell BTCUSDT (当价格跌到 sl 价格时)
- 止盈单: Sell BTCUSDT (当价格涨到 tp 价格时)

#### 请求示例

##### 普通市价单 (Account Risk 模式)
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "order_type": "Market",
  "quantity": 1,
  "sl": "1000",
  "tp": "2000",
  "test_mode": false,
  "bracket_order": false
}
```

##### Bracket Order 市价单
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "order_type": "Market",
  "quantity": 1,
  "sl": "45000",
  "tp": "55000",
  "test_mode": false,
  "bracket_order": true
}
```

##### 限价单 Bracket Order
```json
{
  "product_name": "ETHUSDT",
  "direction": "Sell",
  "order_type": "Limit",
  "quantity": 2,
  "price": 3000,
  "sl": "3100",
  "tp": "2800",
  "test_mode": false,
  "bracket_order": true
}
```

#### 响应格式

**成功响应:**
```json
{
  "success": true,
  "message": "下單準備完成: 方向=Buy, 商品=BTCUSDT, 類型=Market, 數量=1, 止損=45000, 止盈=55000 (Bracket Order模式 - 獨立訂單)"
}
```

**错误响应:**
```json
{
  "success": false,
  "message": "商品名稱不能為空"
}
```

## 使用示例

### Python 示例

```python
import requests
import json

# API 端点
url = "http://localhost:8168/place_order"

# Bracket Order 示例
data = {
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "order_type": "Market",
    "quantity": 1,
    "sl": "45000",      # 止损价格
    "tp": "55000",      # 止盈价格
    "test_mode": False,
    "bracket_order": True
}

response = requests.post(url, json=data)
result = response.json()

if result["success"]:
    print("下单成功:", result["message"])
else:
    print("下单失败:", result["message"])
```

### cURL 示例

```bash
curl -X POST http://localhost:8168/place_order \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "order_type": "Market",
    "quantity": 1,
    "sl": "45000",
    "tp": "55000",
    "test_mode": false,
    "bracket_order": true
  }'
```

## 注意事项

### 1. 前置条件
- 确保 XPlatformHelper 已启动并成功登录交易平台
- 确保 API 服务器已启动 (状态显示 "API服务器: 已启动")
- 确保网络连接正常

### 2. 测试模式
- `test_mode: true` 时，系统只会准备订单但不会实际下单
- 建议首次使用时先用测试模式验证参数正确性
- 生产环境使用时设置 `test_mode: false`

### 3. 价格设定
- **Limit 订单**: 必须提供 `price` 参数
- **Stop Market 订单**: 必须提供 `price` 参数  
- **Market 订单**: 不需要 `price` 参数

### 4. Bracket Order 特别注意
- Bracket Order 模式下，`sl` 和 `tp` 是**价格**，不是金额
- 系统会自动下多个订单，请确保账户有足够的保证金
- 止损和止盈订单的方向会自动设置为与主单相反

### 5. 错误处理
- 如果任何参数无效，API 会返回具体的错误信息
- 网络错误或平台问题也会在响应中说明
- 建议在客户端实现重试机制

### 6. 并发限制
- 建议不要同时发送大量请求
- 每个请求之间建议间隔至少 1-2 秒
- 系统会按顺序处理请求

## 故障排除

### 常见错误

1. **"找不到設定按鈕"**
   - 检查是否已登录交易平台
   - 确认当前页面是交易页面

2. **"商品名稱不能為空"**
   - 检查 `product_name` 参数是否正确传递

3. **"價格必須大於0"**
   - 检查 Limit/Stop Market 订单的 `price` 参数

4. **连接错误**
   - 确认 API 服务器已启动
   - 检查端口 8168 是否被占用
   - 确认防火墙设置

### 调试建议

1. 先使用测试模式验证参数
2. 检查 XPlatformHelper 主界面的状态信息
3. 查看控制台输出的详细错误信息
4. 确认交易平台的网页状态正常

## 版本信息

- API 版本: 1.0
- 支持的订单类型: Market, Limit, Stop Market
- 支持的交易方向: Buy, Sell
- Bracket Order 功能: 已支持
