# XPlatformHelper API 文档

## 概述

XPlatformHelper 提供了一个 REST API 服务器，允许通过 HTTP 请求进行自动化交易操作。API 服务器运行在端口 8168 上。

## 启动 API 服务器

1. 运行 XPlatformHelper 主程序
2. 点击 "启动API" 按钮
3. 确认看到 "API服务器: 已启动" 状态

## API 端点

### POST /place_order

执行下单操作

**URL:** `http://localhost:8168/place_order`

**方法:** POST

**Content-Type:** application/json

#### 请求参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| product_name | string | 是 | - | 商品名称 (如: "BTCUSDT", "ETHUSDT") |
| direction | string | 是 | "Buy" | 交易方向: "Buy" 或 "Sell" |
| order_type | string | 否 | "Market" | 订单类型: "Market", "Limit", "Stop Market" |
| quantity | integer | 否 | 1 | 交易数量 (正整数) |
| sl | string | 否 | "0" | 止损价格 (Bracket Order 模式使用) |
| tp | string | 否 | "0" | 止盈价格 (Bracket Order 模式使用) |
| sldollars | string | 否 | "0" | 止损金额 (普通模式使用) |
| tpdollars | string | 否 | "0" | 止盈金额 (普通模式使用) |
| price | number | 否 | null | 价格 (Limit/Stop Market 订单必需) |
| test_mode | boolean | 否 | true | 是否为测试模式 |
| bracket_order | boolean | 否 | null | 是否启用 Bracket Order (null时根据GUI状态) |

#### 重要说明

##### 新的参数系统

**现在支持两套独立的 SL/TP 参数：**

1. **价格参数 (Bracket Order 专用)**
   - `sl` - 止损价格
   - `tp` - 止盈价格
   - 用于 Bracket Order 模式，表示具体的价格点

2. **金额参数 (普通模式专用)**
   - `sldollars` - 止损金额
   - `tpdollars` - 止盈金额
   - 用于普通模式，表示损失/盈利的金额

##### bracket_order 参数逻辑

**bracket_order 参数有三种状态：**

1. **明确指定 true**
   - 强制使用 Bracket Order 模式
   - 使用 `sl`/`tp` 参数（价格）
   - 忽略 GUI 设置

2. **明确指定 false**
   - 强制使用普通模式
   - 使用 `sldollars`/`tpdollars` 参数（金额）
   - 忽略 GUI 设置

3. **不指定 (null/undefined)**
   - 自动根据 GUI 的复选框状态决定
   - GUI 勾选 → Bracket Order 模式 → 使用 `sl`/`tp`
   - GUI 未勾选 → 普通模式 → 使用 `sldollars`/`tpdollars`

##### Bracket Order 下单逻辑

当 `bracket_order: true` 时，系统会分三个独立步骤下单：

1. **步骤1 - 主要订单**:
   - 设置订单类型 (用户指定)
   - 设置价格 (如果是限价单)
   - 设置数量
   - 按下单按钮

2. **步骤2 - 止损订单** (如果 `sl != "0"`):
   - **做多时**: 设置 Stop Market → 输入 sl 价格 → 输入数量 → 按 Sell 按钮
   - **做空时**: 设置 Stop Market → 输入 sl 价格 → 输入数量 → 按 Buy 按钮

3. **步骤3 - 止盈订单** (如果 `tp != "0"`):
   - **做多时**: 设置 Limit → 输入 tp 价格 → 输入数量 → 按 Sell 按钮
   - **做空时**: 设置 Limit → 输入 tp 价格 → 输入数量 → 按 Buy 按钮

##### 订单类型说明

**做多 (Buy) 交易**：
- 主单: Buy (用户指定类型)
- 止损: Sell Stop Market (价格下跌时止损)
- 止盈: Sell Limit (价格上涨时止盈)

**做空 (Sell) 交易**：
- 主单: Sell (用户指定类型)
- 止损: Buy Stop Market (价格上涨时止损)
- 止盈: Buy Limit (价格下跌时止盈)

##### 灵活设置

- **可以只设置止损**: 设置 `sl`，`tp` 为 "0" 或不设置
- **可以只设置止盈**: 设置 `tp`，`sl` 为 "0" 或不设置
- **可以同时设置**: 设置 `sl` 和 `tp`
- **至少设置一个**: 必须设置 `sl` 或 `tp` 其中至少一个

**示例1 (Buy BTCUSDT Market - 完整设置):**
- 步骤1: 设置 Market → 输入数量1 → Buy BTCUSDT (主单)
- 步骤2: 设置 Stop Market → 输入价格45000 → 输入数量1 → Sell BTCUSDT (止损)
- 步骤3: 设置 Limit → 输入价格55000 → 输入数量1 → Sell BTCUSDT (止盈)

**示例2 (Sell ETHUSDT Limit @ 3000 - 完整设置):**
- 步骤1: 设置 Limit → 输入价格3000 → 输入数量2 → Sell ETHUSDT (主单)
- 步骤2: 设置 Stop Market → 输入价格3100 → 输入数量2 → Buy ETHUSDT (止损)
- 步骤3: 设置 Limit → 输入价格2800 → 输入数量2 → Buy ETHUSDT (止盈)

**示例3 (Buy BTCUSDT - 只设置止损):**
```json
{
  "sl": "45000",
  "tp": "0"  // 或不设置tp
}
```
- 步骤1: Buy BTCUSDT Market (主单)
- 步骤2: Sell BTCUSDT Stop Market @ 45000 (止损)
- 跳过步骤3 (无止盈)

**示例4 (Sell ETHUSDT - 只设置止盈):**
```json
{
  "sl": "0",  // 或不设置sl
  "tp": "2800"
}
```
- 步骤1: Sell ETHUSDT Market (主单)
- 跳过步骤2 (无止损)
- 步骤3: Buy ETHUSDT Limit @ 2800 (止盈)

#### 请求示例

##### 普通市价单 (Account Risk 模式)
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "order_type": "Market",
  "quantity": 1,
  "sldollars": "1000",
  "tpdollars": "2000",
  "test_mode": false,
  "bracket_order": false
}
```

##### Bracket Order 市价单
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "order_type": "Market",
  "quantity": 1,
  "sl": "45000",
  "tp": "55000",
  "test_mode": false,
  "bracket_order": true
}
```

##### 限价单 Bracket Order
```json
{
  "product_name": "ETHUSDT",
  "direction": "Sell",
  "order_type": "Limit",
  "quantity": 2,
  "price": 3000,
  "sl": "3100",
  "tp": "2800",
  "test_mode": false,
  "bracket_order": true
}
```

##### 根据 GUI 状态自动选择模式
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "order_type": "Market",
  "quantity": 1,
  "sl": "45000",
  "tp": "55000",
  "sldollars": "1000",
  "tpdollars": "2000",
  "test_mode": false
  // 不指定 bracket_order，系统会根据 GUI 复选框状态自动选择参数
}
```

#### 响应格式

**成功响应:**
```json
{
  "success": true,
  "message": "下單準備完成: 方向=Buy, 商品=BTCUSDT, 類型=Market, 數量=1, 止損=45000, 止盈=55000 (Bracket Order模式 - 獨立訂單)"
}
```

**错误响应:**
```json
{
  "success": false,
  "message": "商品名稱不能為空"
}
```

## 使用示例

### Python 示例

```python
import requests
import json

# API 端点
url = "http://localhost:8168/place_order"

# 示例1: 明确指定 Bracket Order 模式
bracket_order_data = {
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "order_type": "Market",
    "quantity": 1,
    "sl": "45000",      # 止损价格
    "tp": "55000",      # 止盈价格
    "test_mode": False,
    "bracket_order": True
}

# 示例2: 明确指定普通模式
normal_order_data = {
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "order_type": "Market",
    "quantity": 1,
    "sldollars": "1000",  # 止损金额
    "tpdollars": "2000",  # 止盈金额
    "test_mode": False,
    "bracket_order": False
}

# 示例3: 根据 GUI 状态自动选择
auto_mode_data = {
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "order_type": "Market",
    "quantity": 1,
    "sl": "45000",        # 如果 GUI 勾选了 Bracket Order 就用这个
    "tp": "55000",        # 如果 GUI 勾选了 Bracket Order 就用这个
    "sldollars": "1000",  # 如果 GUI 没勾选就用这个
    "tpdollars": "2000",  # 如果 GUI 没勾选就用这个
    "test_mode": False
    # 不指定 bracket_order，让系统根据 GUI 状态决定
}

# 发送请求
response = requests.post(url, json=auto_mode_data)
result = response.json()

if result["success"]:
    print("下单成功:", result["message"])
else:
    print("下单失败:", result["message"])
```

### cURL 示例

```bash
curl -X POST http://localhost:8168/place_order \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "BTCUSDT",
    "direction": "Buy",
    "order_type": "Market",
    "quantity": 1,
    "sl": "45000",
    "tp": "55000",
    "test_mode": false,
    "bracket_order": true
  }'
```

## 注意事项

### 1. 前置条件
- 确保 XPlatformHelper 已启动并成功登录交易平台
- 确保 API 服务器已启动 (状态显示 "API服务器: 已启动")
- 确保网络连接正常

### 2. 测试模式
- `test_mode: true` 时，系统只会准备订单但不会实际下单
- 建议首次使用时先用测试模式验证参数正确性
- 生产环境使用时设置 `test_mode: false`

### 3. 价格设定
- **Limit 订单**: 必须提供 `price` 参数
- **Stop Market 订单**: 必须提供 `price` 参数  
- **Market 订单**: 不需要 `price` 参数

### 4. Bracket Order 特别注意
- Bracket Order 模式下，`sl` 和 `tp` 是**价格**，不是金额
- 系统会自动下多个订单，请确保账户有足够的保证金
- 止损和止盈订单的方向会自动设置为与主单相反

### 5. 错误处理
- 如果任何参数无效，API 会返回具体的错误信息
- 网络错误或平台问题也会在响应中说明
- 建议在客户端实现重试机制

### 6. 并发限制
- 建议不要同时发送大量请求
- 每个请求之间建议间隔至少 1-2 秒
- 系统会按顺序处理请求

## 故障排除

### 常见错误

1. **"找不到設定按鈕"**
   - 检查是否已登录交易平台
   - 确认当前页面是交易页面

2. **"商品名稱不能為空"**
   - 检查 `product_name` 参数是否正确传递

3. **"價格必須大於0"**
   - 检查 Limit/Stop Market 订单的 `price` 参数

4. **连接错误**
   - 确认 API 服务器已启动
   - 检查端口 8168 是否被占用
   - 确认防火墙设置

### 调试建议

1. 先使用测试模式验证参数
2. 检查 XPlatformHelper 主界面的状态信息
3. 查看控制台输出的详细错误信息
4. 确认交易平台的网页状态正常

## 版本信息

- API 版本: 1.0
- 支持的订单类型: Market, Limit, Stop Market
- 支持的交易方向: Buy, Sell
- Bracket Order 功能: 已支持
