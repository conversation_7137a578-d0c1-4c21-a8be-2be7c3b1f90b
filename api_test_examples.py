#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XPlatformHelper API 测试示例

这个脚本展示了如何使用 XPlatformHelper 的 API 进行自动化交易
"""

import requests
import json
import time

# API 配置
API_BASE_URL = "http://localhost:8168"
PLACE_ORDER_URL = f"{API_BASE_URL}/place_order"

def test_api_connection():
    """测试 API 连接"""
    try:
        # 发送一个测试请求
        test_data = {
            "product_name": "BTCUSDT",
            "direction": "Buy",
            "test_mode": True,
            "bracket_order": False
        }
        
        response = requests.post(PLACE_ORDER_URL, json=test_data, timeout=10)
        
        if response.status_code == 200:
            print("✅ API 连接成功")
            return True
        else:
            print(f"❌ API 连接失败: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 API 服务器，请确认:")
        print("   1. XPlatformHelper 已启动")
        print("   2. API 服务器已启动 (点击'启动API'按钮)")
        print("   3. 端口 8168 未被占用")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def example_normal_order():
    """示例1: 普通订单 (Account Risk 模式)"""
    print("\n=== 示例1: 普通订单 (Account Risk 模式) ===")

    data = {
        "product_name": "BTCUSDT",
        "direction": "Buy",
        "order_type": "Market",
        "quantity": 1,
        "sldollars": "1000", # 金额 (不是价格!)
        "tpdollars": "2000", # 金额 (不是价格!)
        "test_mode": True,   # 测试模式
        "bracket_order": False
    }
    
    print("请求数据:")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(PLACE_ORDER_URL, json=data, timeout=30)
        result = response.json()
        
        print(f"\n响应状态: {response.status_code}")
        print("响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            print("✅ 普通订单测试成功")
        else:
            print("❌ 普通订单测试失败")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def example_bracket_order():
    """示例2: Bracket Order (三步骤下单)"""
    print("\n=== 示例2: Bracket Order (三步骤下单) ===")
    print("这将执行以下步骤:")
    print("1. Buy BTCUSDT Market (主单)")
    print("2. Sell BTCUSDT Stop Market @ 45000 (止损)")
    print("3. Sell BTCUSDT Limit @ 55000 (止盈)")

    data = {
        "product_name": "BTCUSDT",
        "direction": "Buy",
        "order_type": "Market",
        "quantity": 1,
        "sl": "45000",       # 止损价格
        "tp": "55000",       # 止盈价格
        "test_mode": True,   # 测试模式
        "bracket_order": True
    }
    
    print("请求数据:")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(PLACE_ORDER_URL, json=data, timeout=30)
        result = response.json()
        
        print(f"\n响应状态: {response.status_code}")
        print("响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            print("✅ Bracket Order 测试成功")
        else:
            print("❌ Bracket Order 测试失败")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def example_limit_order():
    """示例3: 限价单 Bracket Order (三步骤下单)"""
    print("\n=== 示例3: 限价单 Bracket Order (三步骤下单) ===")
    print("这将执行以下步骤:")
    print("1. Sell ETHUSDT Limit @ 3000 (主单)")
    print("2. Buy ETHUSDT Stop Market @ 3100 (止损)")
    print("3. Buy ETHUSDT Limit @ 2800 (止盈)")

    data = {
        "product_name": "ETHUSDT",
        "direction": "Sell",
        "order_type": "Limit",
        "quantity": 2,
        "price": 3000,       # 主单限价
        "sl": "3100",        # 止损价格
        "tp": "2800",        # 止盈价格
        "test_mode": True,   # 测试模式
        "bracket_order": True
    }
    
    print("请求数据:")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(PLACE_ORDER_URL, json=data, timeout=30)
        result = response.json()
        
        print(f"\n响应状态: {response.status_code}")
        print("响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            print("✅ 限价单 Bracket Order 测试成功")
        else:
            print("❌ 限价单 Bracket Order 测试失败")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def example_auto_mode():
    """示例4: 自动模式选择 (根据GUI状态)"""
    print("\n=== 示例4: 自动模式选择 (根据GUI状态) ===")

    data = {
        "product_name": "BTCUSDT",
        "direction": "Buy",
        "order_type": "Market",
        "quantity": 1,
        "sl": "45000",       # 如果GUI勾选了Bracket Order就用这个(价格)
        "tp": "55000",       # 如果GUI勾选了Bracket Order就用这个(价格)
        "sldollars": "1000", # 如果GUI没勾选就用这个(金额)
        "tpdollars": "2000", # 如果GUI没勾选就用这个(金额)
        "test_mode": True
        # 不指定 bracket_order，让系统根据GUI状态决定
    }

    print("请求数据 (包含两套参数，系统会根据GUI状态自动选择):")
    print(json.dumps(data, indent=2, ensure_ascii=False))

    try:
        response = requests.post(PLACE_ORDER_URL, json=data, timeout=30)
        result = response.json()

        print(f"\n响应状态: {response.status_code}")
        print("响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

        if result.get("success"):
            print("✅ 自动模式选择测试成功")
            print("💡 检查响应消息中的模式信息")
        else:
            print("❌ 自动模式选择测试失败")

    except Exception as e:
        print(f"❌ 请求失败: {e}")

def example_sl_only():
    """示例5: 只设置止损的 Bracket Order"""
    print("\n=== 示例5: 只设置止损的 Bracket Order ===")
    print("这将执行以下步骤:")
    print("1. Sell ETHUSDT Market (主单)")
    print("2. Buy ETHUSDT Stop Market @ 3100 (止损)")
    print("3. 跳过止盈步骤")

    data = {
        "product_name": "ETHUSDT",
        "direction": "Sell",
        "order_type": "Market",
        "quantity": 1,
        "sl": "3100",        # 只设置止损
        "tp": "0",           # 不设置止盈
        "test_mode": True,
        "bracket_order": True
    }

    print("请求数据 (只设置止损):")
    print(json.dumps(data, indent=2, ensure_ascii=False))

    try:
        response = requests.post(PLACE_ORDER_URL, json=data, timeout=30)
        result = response.json()

        print(f"\n响应状态: {response.status_code}")
        print("响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

        if result.get("success"):
            print("✅ 只设置止损测试成功")
            print("💡 系统正确处理了只有止损的情况")
        else:
            print("❌ 只设置止损测试失败")

    except Exception as e:
        print(f"❌ 请求失败: {e}")

def example_error_handling():
    """示例6: 其他错误处理"""
    print("\n=== 示例6: 其他错误处理 ===")

    # 故意发送无效数据
    data = {
        "product_name": "",  # 空商品名称 (会导致错误)
        "direction": "Buy",
        "test_mode": True
    }
    
    print("请求数据 (故意包含错误):")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(PLACE_ORDER_URL, json=data, timeout=30)
        result = response.json()
        
        print(f"\n响应状态: {response.status_code}")
        print("响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if not result.get("success"):
            print("✅ 错误处理测试成功 (正确返回了错误信息)")
        else:
            print("❌ 错误处理测试失败 (应该返回错误)")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("XPlatformHelper API 测试脚本")
    print("=" * 50)
    
    # 测试连接
    if not test_api_connection():
        print("\n请先启动 XPlatformHelper 并确保 API 服务器正在运行")
        return
    
    print("\n重要提醒:")
    print("🔴 普通模式: sldollars/tpdollars 是金额")
    print("🔵 Bracket Order 模式: sl/tp 是价格")
    print("🔄 自动模式: 不指定bracket_order，根据GUI状态选择")
    print("⚠️  以下所有示例都使用测试模式，不会实际下单")

    # 等待用户确认
    input("\n按 Enter 键开始测试...")

    # 运行示例
    example_normal_order()
    time.sleep(2)

    example_bracket_order()
    time.sleep(2)

    example_limit_order()
    time.sleep(2)

    example_auto_mode()
    time.sleep(2)

    example_sl_only()
    time.sleep(2)

    example_error_handling()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n如需实际下单，请:")
    print("1. 将 test_mode 设置为 false")
    print("2. 确认所有参数正确")
    print("3. 确保账户有足够的保证金")

if __name__ == "__main__":
    main()
