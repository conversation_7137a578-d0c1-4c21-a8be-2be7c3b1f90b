# API 参数对照表

## 新的参数系统总览

### 🔄 智能参数选择逻辑

| bracket_order 值 | 使用的参数 | 含义 | 模式 |
|------------------|------------|------|------|
| `true` | `sl`, `tp` | 价格 | Bracket Order |
| `false` | `sldollars`, `tpdollars` | 金额 | 普通模式 |
| `null`/不指定 | 根据GUI状态自动选择 | 动态 | 自动模式 |

### 📋 完整参数列表

#### 基础参数
- `product_name` (string, 必需) - 商品名称
- `direction` (string, 必需) - 交易方向 ("Buy"/"Sell")
- `order_type` (string) - 订单类型 ("Market"/"Limit"/"Stop Market")
- `quantity` (integer) - 交易数量
- `price` (number) - 价格 (限价单必需)
- `test_mode` (boolean) - 测试模式

#### 止损止盈参数 (两套系统)

**价格系统 (Bracket Order 专用)**
- `sl` (string) - 止损价格
- `tp` (string) - 止盈价格

**金额系统 (普通模式专用)**
- `sldollars` (string) - 止损金额
- `tpdollars` (string) - 止盈金额

#### 模式控制参数
- `bracket_order` (boolean/null) - 模式选择器

## 使用场景示例

### 场景1: 明确使用普通模式
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "sldollars": "1000",
  "tpdollars": "2000",
  "bracket_order": false
}
```
**结果**: 使用Account Risk设定，金额为止损止盈标准

### 场景2: 明确使用Bracket Order
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "sl": "45000",
  "tp": "55000",
  "bracket_order": true
}
```
**结果**: 下独立订单，价格为止损止盈标准

### 场景3: 让GUI决定模式
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "sl": "45000",
  "tp": "55000",
  "sldollars": "1000",
  "tpdollars": "2000"
}
```
**结果**: 
- GUI勾选 → 使用sl/tp (价格)
- GUI未勾选 → 使用sldollars/tpdollars (金额)

### 场景4: 混合参数 (推荐)
```json
{
  "product_name": "BTCUSDT",
  "direction": "Buy",
  "sl": "45000",
  "tp": "55000",
  "sldollars": "1000",
  "tpdollars": "2000",
  "bracket_order": null
}
```
**优势**: 
- 提供两套完整参数
- 系统根据实际需要自动选择
- 最大灵活性

## 迁移指南

### 从旧版本升级

**旧版本 API 调用:**
```json
{
  "sl": "1000",
  "tp": "2000",
  "bracket_order": false
}
```

**新版本等效调用:**
```json
{
  "sldollars": "1000",
  "tpdollars": "2000",
  "bracket_order": false
}
```

### 向后兼容性

- 旧的 `sl`/`tp` 参数仍然支持
- 当 `bracket_order: false` 时，`sl`/`tp` 会被当作金额处理
- 建议逐步迁移到新的参数系统

## 最佳实践

### 1. 明确指定模式
```python
# 推荐: 明确指定使用哪种模式
data = {
    "bracket_order": True,  # 明确指定
    "sl": "45000",         # 对应的参数
    "tp": "55000"
}
```

### 2. 提供完整参数集
```python
# 推荐: 提供两套参数，让系统选择
data = {
    "sl": "45000",         # Bracket Order 用
    "tp": "55000",
    "sldollars": "1000",   # 普通模式用
    "tpdollars": "2000"
    # 不指定 bracket_order，根据 GUI 状态
}
```

### 3. 错误处理
```python
# 检查响应消息了解实际使用的模式
response = requests.post(url, json=data)
result = response.json()

if "Bracket Order模式" in result.get("message", ""):
    print("使用了 Bracket Order 模式")
elif "Account Risk設定" in result.get("message", ""):
    print("使用了普通模式")
```

## 常见问题

### Q: 如果同时提供了两套参数会怎样？
A: 系统会根据 `bracket_order` 参数或 GUI 状态选择使用哪套参数，另一套会被忽略。

### Q: 如果忘记提供对应的参数会怎样？
A: 系统会使用默认值 "0"，或返回错误信息。

### Q: GUI 状态如何影响 API？
A: 只有当 `bracket_order` 参数为 `null` 或不提供时，系统才会检查 GUI 的复选框状态。

### Q: 旧的 API 调用还能用吗？
A: 可以，但建议迁移到新的参数系统以获得更好的清晰度和功能。
