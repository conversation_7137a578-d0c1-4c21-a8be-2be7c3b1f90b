from selenium.webdriver.common.by import By
from selenium.common.exceptions import NoSuchElementException

class SeleniumHelper:
    def __init__(self, driver=None):
        self.driver = driver
        
    def set_driver(self, driver):
        """設置 WebDriver"""
        self.driver = driver
        
    def get_operation_button(self, aria_label):
        """獲取操作按鈕
        Args:
            aria_label: 按鈕的 aria-label 屬性值，例如 'Log Out', 'Settings' 等
        Returns:
            tuple: (按鈕元素, 錯誤信息)，如果找不到則返回 (None, 錯誤信息)
        """
        try:
            if not self.driver:
                return None, "WebDriver 未初始化"
                
            # 找到指定的圖標，使用更通用的選擇器
            icon = self.driver.find_element(By.CSS_SELECTOR, 
                f"[aria-label='{aria_label}']")
            
            # 獲取父級div（按鈕容器）
            button_div = icon.find_element(By.XPATH, "./..")
            
            return button_div, None
            
        except Exception as e:
            error_msg = f"找不到 {aria_label} 按鈕: {str(e)}"
            print(error_msg)
            return None, error_msg

    def find_div_by_text(self, text):
        """找到包含指定文字的div元素
        Args:
            text: div中的文字內容
        Returns:
            tuple: (元素, 錯誤信息)，如果找不到則返回 (None, 錯誤信息)
        """
        try:
            if not self.driver:
                return None, "WebDriver 未初始化"
            
            # 使用XPath找到包含指定文字的div
            div = self.driver.find_element(By.XPATH, 
                f"//div[contains(text(), '{text}')]")
            
            return div, None
            
        except Exception as e:
            error_msg = f"找不到文字為 '{text}' 的div: {str(e)}"
            print(error_msg)
            return None, error_msg

    def find_sl_tp_inputs(self):
        """找到止損和止盈的輸入框
        Returns:
            tuple: (止損輸入框, 止盈輸入框, 錯誤信息)
        """
        try:
            if not self.driver:
                return None, None, "WebDriver 未初始化"
            
            # 找到所有符合條件的數字輸入框
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 
                "input[type='number'][min='0']")
            
            sl_input = None
            tp_input = None
            
            for input_elem in inputs:
                try:
                    # 獲取父元素的父元素
                    parent = input_elem.find_element(By.XPATH, "../..")
                    parent_text = parent.text.lower()
                    
                    # 檢查文字內容
                    if "risk" in parent_text:
                        sl_input = input_elem
                    elif "profit" in parent_text:
                        tp_input = input_elem
                        
                    # 如果都找到了就可以退出循環
                    if sl_input and tp_input:
                        break
                        
                except Exception:
                    continue
            
            return sl_input, tp_input, None
            
        except Exception as e:
            error_msg = f"找不到止損/止盈輸入框: {str(e)}"
            print(error_msg)
            return None, None, error_msg

    def find_button_by_text(self, text):
        """
        查找指定文本的按鈕，支持普通按鈕和MUI按鈕
        :param text: 按鈕文本
        :return: (button_element, error_message)
        """
        try:
            # 使用XPath查找包含指定文本的MUI按鈕
            button = self.driver.find_element(By.XPATH, 
                f"//button[contains(@class, 'MuiButton-root') and normalize-space()='{text}']")
            return button, None
        except NoSuchElementException:
            return None, f"找不到文本為 '{text}' 的按鈕"
        except Exception as e:
            return None, f"查找按鈕時發生錯誤: {str(e)}"

    def find_limit_price_input(self):
        """找到價格輸入框
        Returns:
            WebElement or None: 找到的價格輸入框元素，如果找不到則返回 None
        """
        try:
            if not self.driver:
                return None
            
            # 找到所有數字輸入框
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 
                "#orderCardTab input[type='number']")
            
            # 遍歷每個輸入框
            for input_elem in inputs:
                try:
                    # 獲取父元素的父元素
                    parent = input_elem.find_element(By.XPATH, "../..")
                    parent_text = parent.text.lower()
                    
                    # 檢查是否包含 "limit price" 或 "stop price"
                    if "limit price" in parent_text or "stop price" in parent_text:
                        return input_elem
                        
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            print(f"找不到價格輸入框: {str(e)}")
            return None

   