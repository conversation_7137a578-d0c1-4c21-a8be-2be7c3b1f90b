from selenium import webdriver
from selenium.webdriver.firefox.service import Service
from webdriver_manager.firefox import GeckoDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json
import tkinter as tk
from tkinter import ttk
import threading
from queue import Queue
import re

class XPlatformGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("XPlatform Helper")
        self.root.geometry("800x600")
        self.helper = None
        self.is_tracking = False
        self.tracking_thread = None
        self.queue = Queue()
        self.setup_gui()
        
    def setup_gui(self):
        # Control Frame
        control_frame = ttk.Frame(self.root, padding="10")
        control_frame.pack(fill=tk.X)
        
        # URL Input
        self.url_var = tk.StringVar(value="https://www.google.com")
        ttk.Label(control_frame, text="URL:").pack(side=tk.LEFT)
        ttk.Entry(control_frame, textvariable=self.url_var, width=50).pack(side=tk.LEFT, padx=5)
        
        # Buttons Frame
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.LEFT, padx=5)
        
        # Browser Control Buttons
        self.start_btn = ttk.Button(button_frame, text="Launch Browser", command=self.start_browser)
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.track_btn = ttk.Button(button_frame, text="Start Tracking", command=self.toggle_tracking, state=tk.DISABLED)
        self.track_btn.pack(side=tk.LEFT, padx=2)
        
        # Exit Button with red color
        exit_frame = ttk.Frame(control_frame)
        exit_frame.pack(side=tk.RIGHT)
        exit_btn = tk.Button(exit_frame, text="Exit", command=self.quit_app, 
                           bg='red', fg='white', width=8)
        exit_btn.pack(padx=5)
        
        # Info Display Area
        info_frame = ttk.Frame(self.root, padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        # Add scrollbar to text widget
        text_frame = ttk.Frame(info_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.info_text = tk.Text(text_frame, wrap=tk.WORD, height=10)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status Bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=2)
        
    def start_browser(self):
        if not self.helper:
            self.helper = XPlatformHelper()
            self.helper.setup_driver()
            self.navigate_to_url()
            self.track_btn.config(state=tk.NORMAL)
            self.start_btn.config(text="Reload Page")
            self.status_var.set("Browser launched")
        else:
            self.navigate_to_url()
            
    def navigate_to_url(self):
        if self.helper:
            url = self.url_var.get()
            # 如果正在追蹤，先停止追蹤
            if self.is_tracking:
                self.stop_tracking_ui()
                self.helper.enable_tracking(False)
            # 導航到新頁面
            self.helper.navigate_to(url)
            self.status_var.set(f"Navigated to: {url}")
            # 重置追蹤狀態
            self.is_tracking = False
            self.track_btn.config(text="Start Tracking")
            
    def toggle_tracking(self):
        if not self.is_tracking:
            if self.helper:
                # 獲取當前瀏覽器URL並更新GUI
                try:
                    current_url = self.helper.get_current_url()
                    self.url_var.set(current_url)
                    # 重新注入追蹤腳本
                    self.helper.inject_mouse_tracking()
                except Exception as e:
                    print(f"Error getting current URL: {str(e)}")
                    
            self.is_tracking = True
            self.track_btn.config(text="Stop Tracking")
            if self.helper:
                self.helper.enable_tracking(True)
            self.tracking_thread = threading.Thread(target=self.tracking_loop)
            self.tracking_thread.daemon = True
            self.tracking_thread.start()
            self.status_var.set("Tracking elements")
        else:
            self.is_tracking = False
            if self.helper:
                self.helper.enable_tracking(False)
            self.track_btn.config(text="Start Tracking")
            self.status_var.set("Tracking stopped")
            
    def tracking_loop(self):
        while self.is_tracking:
            if self.helper:
                try:
                    info = self.helper.get_hovered_element_info()
                    if info:
                        self.queue.put(info)
                        self.root.after(1, self.update_info_text)
                        self.root.after(1, self.stop_tracking_ui)
                except Exception as e:
                    print(f"Tracking error: {str(e)}")
            time.sleep(0.1)
            
    def update_info_text(self):
        try:
            while not self.queue.empty():
                info = self.queue.get_nowait()
                self.info_text.delete(1.0, tk.END)
                self.info_text.insert(tk.END, info)
        except Exception as e:
            print(f"UI update error: {str(e)}")
            
    def quit_app(self):
        self.is_tracking = False
        if self.helper:
            self.helper.close_driver()
        self.root.quit()
        
    def run(self):
        self.root.mainloop()

    def stop_tracking_ui(self):
        """更新UI狀態為停止追蹤"""
        self.is_tracking = False
        if self.helper:
            self.helper.enable_tracking(False)
        self.track_btn.config(text="Start Tracking")
        self.status_var.set("Element info collected")

class XPlatformHelper:
    def __init__(self):
        self.driver = None
        self.last_element_info = None
        self._script_injected = False  # 添加腳本注入標記
        
    def setup_driver(self):
        service = Service(GeckoDriverManager().install())
        options = webdriver.FirefoxOptions()
        options.set_preference("dom.webdriver.enabled", False)
        options.set_preference('useAutomationExtension', False)
        self.driver = webdriver.Firefox(service=service, options=options)
        
    def inject_mouse_tracking(self):
        """只在第一次調用時注入腳本"""
        if not self._script_injected:
            tracking_script = """
            if (!window._mouseTrackingInitialized) {
                window._mouseTrackingInitialized = true;
                window._trackingEnabled = false;
                
                var style = document.createElement('style');
                style.innerHTML = `
                    .selenium-mouse-over { 
                        outline: 2px solid red !important;
                        position: relative;
                    }
                    .selenium-timer {
                        position: fixed;
                        background: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 2px 6px;
                        border-radius: 3px;
                        font-size: 12px;
                        z-index: 999999;
                        display: none;
                    }
                `;
                document.head.appendChild(style);

                // 添加計時器變量
                window._hoverTimer = null;
                window._lastHoveredTime = 0;
                window._hoverDelay = 3000; // 3秒延遲
                window._timerElement = null;
                window._timerInterval = null;
                window._lastMouseX = 0;
                window._lastMouseY = 0;
                window._lastHoveredElement = null;

                // 創建計時器顯示元素
                function createTimer() {
                    if (!window._timerElement) {
                        window._timerElement = document.createElement('div');
                        window._timerElement.className = 'selenium-timer';
                        document.body.appendChild(window._timerElement);
                    }
                }

                // 更新計時器顯示
                function updateTimer(x, y, remainingTime) {
                    if (!window._timerElement) createTimer();
                    window._timerElement.style.left = (x + 20) + 'px';
                    window._timerElement.style.top = (y + 20) + 'px';
                    window._timerElement.textContent = (remainingTime / 1000).toFixed(1) + 's';
                    window._timerElement.style.display = 'block';
                }

                // 隱藏計時器
                function hideTimer() {
                    if (window._timerElement) {
                        window._timerElement.style.display = 'none';
                    }
                    if (window._timerInterval) {
                        clearInterval(window._timerInterval);
                        window._timerInterval = null;
                    }
                }

                // 開始計時器
                function startTimer(x, y) {
                    window._lastMouseX = x;
                    window._lastMouseY = y;
                    window._lastHoveredTime = Date.now();
                    
                    // 清除之前的計時器
                    if (window._timerInterval) {
                        clearInterval(window._timerInterval);
                    }
                    
                    // 設置新的計時器
                    window._timerInterval = setInterval(function() {
                        var elapsed = Date.now() - window._lastHoveredTime;
                        var remaining = Math.max(0, window._hoverDelay - elapsed);
                        
                        if (remaining > 0) {
                            updateTimer(window._lastMouseX, window._lastMouseY, remaining);
                        } else {
                            hideTimer();
                            clearInterval(window._timerInterval);
                            window._timerInterval = null;
                            
                            // 收集元素信息
                            if (!window._currentElementInfo && window._lastHoveredElement) {
                                collectElementInfo(window._lastHoveredElement);
                                // 自動停止追蹤
                                window._trackingEnabled = false;
                                if (window._lastHoveredElement) {
                                    window._lastHoveredElement.classList.remove('selenium-mouse-over');
                                    window._lastHoveredElement = null;
                                }
                            }
                        }
                    }, 100); // 每0.1秒更新一次
                }

                // 收集元素信息
                function collectElementInfo(element) {
                    var rect = element.getBoundingClientRect();
                    var computedStyle = window.getComputedStyle(element);
                    
                    // Get full path
                    function getFullPath(el) {
                        let path = [];
                        while (el && el.nodeType === Node.ELEMENT_NODE) {
                            let selector = el.nodeName.toLowerCase();
                            if (el.id) {
                                selector += '#' + el.id;
                            } else {
                                let sib = el, nth = 1;
                                while (sib.previousElementSibling) {
                                    sib = sib.previousElementSibling;
                                    nth++;
                                }
                                selector += ":nth-child("+nth+")";
                            }
                            path.unshift(selector);
                            el = el.parentNode;
                        }
                        return path.join(" > ");
                    }

                    // Get all computed styles
                    function getAllStyles(el) {
                        let styles = {};
                        let computed = window.getComputedStyle(el);
                        for (let i = 0; i < computed.length; i++) {
                            let prop = computed[i];
                            let value = computed.getPropertyValue(prop);
                            if (value && value !== '0px' && value !== 'none' && value !== 'normal') {
                                styles[prop] = value;
                            }
                        }
                        return styles;
                    }

                    // Get all attributes
                    function getAllAttributes(el) {
                        let attrs = {};
                        for (let attr of el.attributes) {
                            attrs[attr.name] = attr.value;
                        }
                        return attrs;
                    }

                    // Get element size and position
                    function getElementMetrics(el) {
                        let rect = el.getBoundingClientRect();
                        return {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height),
                            top: Math.round(rect.top),
                            right: Math.round(rect.right),
                            bottom: Math.round(rect.bottom),
                            left: Math.round(rect.left)
                        };
                    }

                    var elementInfo = {
                        tag: element.tagName,
                        id: element.id || '',
                        class: element.className || '',
                        text: (element.textContent || '').trim().substring(0, 100),
                        href: element.href || '',
                        type: element.type || '',
                        value: element.value || '',
                        placeholder: element.placeholder || '',
                        name: element.name || '',
                        fullPath: getFullPath(element),
                        childCount: element.children.length,
                        hasChildNodes: element.hasChildNodes(),
                        isVisible: !(computedStyle.display === 'none' || computedStyle.visibility === 'hidden'),
                        isEnabled: !element.disabled,
                        isContentEditable: element.isContentEditable,
                        dataset: Object.assign({}, element.dataset),
                        metrics: getElementMetrics(element),
                        attributes: getAllAttributes(element),
                        styles: getAllStyles(element)
                    };
                    
                    window._currentElementInfo = JSON.stringify(elementInfo);
                }

                if (window._mouseMoveHandler) {
                    document.removeEventListener('mousemove', window._mouseMoveHandler);
                }

                window._mouseMoveHandler = function(e) {
                    if (!window._trackingEnabled) return;
                    try {
                        var currentElement = document.elementFromPoint(e.clientX, e.clientY);
                        if (!currentElement) return;

                        // 如果元素改變，重置計時器
                        if (window._lastHoveredElement !== currentElement) {
                            if (window._lastHoveredElement) {
                                window._lastHoveredElement.classList.remove('selenium-mouse-over');
                            }
                            window._lastHoveredElement = currentElement;
                            currentElement.classList.add('selenium-mouse-over');
                            window._currentElementInfo = null;  // 清除之前的元素信息
                            
                            // 重新開始計時
                            startTimer(e.clientX, e.clientY);
                        }
                    } catch (err) {
                        console.error('Mouse tracking error:', err);
                    }
                };

                // 添加鼠標移出頁面的處理
                document.addEventListener('mouseleave', function() {
                    if (!window._trackingEnabled) return;
                    hideTimer();
                    if (window._lastHoveredElement) {
                        window._lastHoveredElement.classList.remove('selenium-mouse-over');
                        window._lastHoveredElement = null;
                    }
                    window._currentElementInfo = null;
                    window._lastHoveredTime = 0;
                    window._lastMouseX = 0;
                    window._lastMouseY = 0;
                });

                // 添加窗口失焦處理
                window.addEventListener('blur', function() {
                    if (!window._trackingEnabled) return;
                    hideTimer();
                    if (window._lastHoveredElement) {
                        window._lastHoveredElement.classList.remove('selenium-mouse-over');
                        window._lastHoveredElement = null;
                    }
                    window._currentElementInfo = null;
                    window._lastHoveredTime = 0;
                    window._lastMouseX = 0;
                    window._lastMouseY = 0;
                });

                document.addEventListener('mousemove', window._mouseMoveHandler);
                console.log('Mouse tracking initialized with 3s delay and timer');
            }
            """
            try:
                self.driver.execute_script(tracking_script)
                self._script_injected = True
                time.sleep(1)
            except Exception as e:
                print(f"Script injection error: {str(e)}")
        
    def get_hovered_element_info(self):
        try:
            element_info = self.driver.execute_script("return window._currentElementInfo || null;")
            if element_info and element_info != "null":
                info = json.loads(element_info)
                if info != self.last_element_info:
                    self.last_element_info = info
                    # 如果獲取到新的元素信息，自動停止追蹤
                    self.driver.execute_script("""
                    if (window._mouseTrackingInitialized && window._timerElement) {
                        window._trackingEnabled = false;
                        if (window._lastHoveredElement) {
                            window._lastHoveredElement.classList.remove('selenium-mouse-over');
                            window._lastHoveredElement = null;
                        }
                        window._timerElement.style.display = 'none';
                        if (window._timerInterval) {
                            clearInterval(window._timerInterval);
                            window._timerInterval = null;
                        }
                    }
                    """)
                    self.is_tracking = False
                    return self._format_element_info(info)
        except Exception as e:
            if str(e) != "null":
                return f"Element info error: {str(e)}"
        return None

    def _format_element_info(self, info):
        lines = []
        lines.append("="*50)
        lines.append(f"Basic Information:")
        lines.append(f"  Tag: {info['tag'].lower()}")
        lines.append(f"  Full Path: {info['fullPath']}")
        
        if info['id']:
            lines.append(f"  ID: {info['id']}")
        
        if info['class']:
            lines.append(f"  Class: {info['class']}")
            
        if info['text']:
            lines.append(f"  Text: {info['text']}")
            
        if info['href']:
            lines.append(f"  Link: {info['href']}")
            
        if info['name']:
            lines.append(f"  Name: {info['name']}")
            
        if info['type']:
            lines.append(f"  Type: {info['type']}")
            
        if info['value']:
            lines.append(f"  Value: {info['value']}")
            
        if info['placeholder']:
            lines.append(f"  Placeholder: {info['placeholder']}")

        lines.append(f"\nElement Properties:")
        lines.append(f"  Child Elements: {info['childCount']}")
        lines.append(f"  Has Child Nodes: {info['hasChildNodes']}")
        lines.append(f"  Is Visible: {info['isVisible']}")
        lines.append(f"  Is Enabled: {info['isEnabled']}")
        lines.append(f"  Is Content Editable: {info['isContentEditable']}")

        if info['dataset']:
            lines.append(f"\nData Attributes:")
            for key, value in info['dataset'].items():
                lines.append(f"  data-{key}: {value}")

        lines.append(f"\nMetrics:")
        for key, value in info['metrics'].items():
            lines.append(f"  {key}: {value}px")

        if info['attributes']:
            lines.append(f"\nAttributes:")
            for key, value in info['attributes'].items():
                if key not in ['id', 'class', 'style']:  # Skip already shown attributes
                    lines.append(f"  {key}: {value}")

        lines.append(f"\nKey Styles:")
        important_styles = [
            'display', 'position', 'visibility', 'z-index',
            'background-color', 'color', 'font-family', 'font-size',
            'margin', 'padding', 'border', 'cursor',
            'flex', 'grid', 'opacity', 'transform'
        ]
        
        for style in important_styles:
            if style in info['styles']:
                lines.append(f"  {style}: {info['styles'][style]}")
        
        lines.append("\nAll Styles:")
        for key, value in info['styles'].items():
            if key not in important_styles:  # Skip already shown styles
                lines.append(f"  {key}: {value}")
                
        lines.append("="*50)
        return "\n".join(lines)
            
    def navigate_to(self, url):
        if not self.driver:
            self.setup_driver()
        try:
            self.driver.get(url)
            time.sleep(1)
            # 重置腳本注入狀態，這樣在新頁面會重新注入
            self._script_injected = False
        except Exception as e:
            print(f"Navigation error: {str(e)}")
        
    def close_driver(self):
        if self.driver:
            self.driver.quit()
            self.driver = None

    def enable_tracking(self, enabled=True):
        try:
            if enabled:
                # 在啟用追蹤時注入腳本
                self.inject_mouse_tracking()
            self.driver.execute_script(f"window._trackingEnabled = {str(enabled).lower()};")
            if not enabled:
                # 清理所有追蹤狀態
                cleanup_script = """
                if (window._mouseTrackingInitialized && window._timerElement) {
                    window._timerElement.style.display = 'none';
                    if (window._timerInterval) {
                        clearInterval(window._timerInterval);
                        window._timerInterval = null;
                    }
                    if (window._lastHoveredElement) {
                        window._lastHoveredElement.classList.remove('selenium-mouse-over');
                        window._lastHoveredElement = null;
                    }
                    window._currentElementInfo = null;
                    window._lastHoveredTime = 0;
                    window._lastMouseX = 0;
                    window._lastMouseY = 0;
                }
                """
                self.driver.execute_script(cleanup_script)
        except Exception as e:
            print(f"Tracking toggle error: {str(e)}")

    def get_current_url(self):
        """獲取當前瀏覽器URL"""
        if self.driver:
            return self.driver.current_url
        return None

def main():
    app = XPlatformGUI()
    app.run()

if __name__ == "__main__":
    main()