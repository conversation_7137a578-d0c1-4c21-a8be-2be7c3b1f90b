from selenium import webdriver
import tkinter as tk
from tkinter import ttk
import threading
import time
from login import LoginHelper
from order import OrderHelper
from api_server import APIServer
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

class MonitorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Monitor")
        self.root.geometry("600x700")  # 增加初始高度
        self.root.minsize(600, 700)    # 設置最小視窗尺寸
        
        self.is_monitoring = False
        self.is_closing = False
        self.initial_sync_done = False  # 添加標記，用於追踪是否已完成初始同步
        self.product_synced = False     # 商品同步標記
        self.order_type_synced = False  # 訂單類型同步標記
        self.quantity_synced = False    # 數量同步標記
        
        self.setup_gui()
        self.start()
        
    def setup_gui(self):
        """設置GUI界面"""
        # 創建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Current URL Display
        url_display_frame = ttk.Frame(main_frame, padding="5")  # 減少padding
        url_display_frame.pack(fill=tk.X)
        ttk.Label(url_display_frame, text="Current URL:").pack(side=tk.LEFT)
        self.current_url_label = ttk.Label(url_display_frame, text="", wraplength=500)
        self.current_url_label.pack(side=tk.LEFT, padx=5)
        
        # Status Display
        status_frame = ttk.Frame(main_frame, padding="5")  # 減少padding
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        # Status Label with larger font
        self.status_label = ttk.Label(status_frame, text="Starting browser...",
                                    font=('Helvetica', 16))
        self.status_label.pack(pady=10)  # 減少間距
        
        # Detection Status with even larger font and bold
        self.detection_label = ttk.Label(status_frame, text="Initializing...",
                                       font=('Helvetica', 24, 'bold'))
        self.detection_label.pack(pady=10)  # 減少間距
        
        # Buttons Frame
        buttons_frame = ttk.Frame(status_frame)
        buttons_frame.pack(fill=tk.X, pady=5)
        
        # Logout Button
        self.logout_button = ttk.Button(buttons_frame, text="登出",
                                      command=self._handle_logout,
                                      state=tk.DISABLED)
        self.logout_button.pack(side=tk.LEFT, padx=5)
        
        # Settings Button
        self.settings_button = ttk.Button(buttons_frame, text="設定",
                                        command=self._handle_settings,
                                        state=tk.DISABLED)
        self.settings_button.pack(side=tk.LEFT, padx=5)
        
        # SL/TP Frame
        sl_tp_frame = ttk.Frame(buttons_frame)
        sl_tp_frame.pack(side=tk.LEFT, padx=20)
        
        # SL Input
        ttk.Label(sl_tp_frame, text="SL:").pack(side=tk.LEFT, padx=2)
        self.sl_var = tk.StringVar()
        self.sl_entry = ttk.Entry(sl_tp_frame, textvariable=self.sl_var, width=8)
        self.sl_entry.pack(side=tk.LEFT, padx=2)
        
        # TP Input
        ttk.Label(sl_tp_frame, text="TP:").pack(side=tk.LEFT, padx=2)
        self.tp_var = tk.StringVar()
        self.tp_entry = ttk.Entry(sl_tp_frame, textvariable=self.tp_var, width=8)
        self.tp_entry.pack(side=tk.LEFT, padx=2)
        
        # Set SL/TP Button
        self.sl_tp_button = ttk.Button(sl_tp_frame, text="設定止損止盈",
                                     command=self._handle_sl_tp,
                                     state=tk.DISABLED)
        self.sl_tp_button.pack(side=tk.LEFT, padx=5)
        
        # Order Input Frame
        order_frame = ttk.LabelFrame(main_frame, text="下單", padding="10")
        order_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Direction Selection
        direction_frame = ttk.Frame(order_frame)
        direction_frame.pack(fill=tk.X, pady=5)
        ttk.Label(direction_frame, text="方向:").pack(side=tk.LEFT, padx=5)
        self.direction_var = tk.StringVar(value="Buy")
        self.direction_combo = ttk.Combobox(direction_frame,
                                          textvariable=self.direction_var,
                                          values=["Buy", "Sell"],
                                          state="readonly",
                                          width=10)
        self.direction_combo.pack(side=tk.LEFT, padx=5)
        
        # Product Input
        product_frame = ttk.Frame(order_frame)
        product_frame.pack(fill=tk.X, pady=5)
        ttk.Label(product_frame, text="商品:").pack(side=tk.LEFT, padx=5)
        self.product_entry = ttk.Entry(product_frame)
        self.product_entry.pack(side=tk.LEFT, padx=5)
        self.product_button = ttk.Button(product_frame, text="切換商品",
                                       command=self._handle_product_change,
                                       state=tk.DISABLED)
        self.product_button.pack(side=tk.LEFT, padx=5)
        
        # Order Type Selection
        type_frame = ttk.Frame(order_frame)
        type_frame.pack(fill=tk.X, pady=5)
        ttk.Label(type_frame, text="類型:").pack(side=tk.LEFT, padx=5)
        self.order_type_var = tk.StringVar(value="Market")
        self.order_type_combo = ttk.Combobox(type_frame, 
                                           textvariable=self.order_type_var,
                                           values=["Market", "Limit", "Stop Market"],
                                           state="readonly",
                                           width=10)
        self.order_type_combo.pack(side=tk.LEFT, padx=5)
        self.order_type_combo.bind('<<ComboboxSelected>>', 
                                 self._handle_order_type_change)
        
        # Price Input Frame
        price_frame = ttk.Frame(order_frame)
        price_frame.pack(fill=tk.X, pady=5)
        ttk.Label(price_frame, text="價格:").pack(side=tk.LEFT, padx=5)
        self.price_var = tk.StringVar()
        self.price_entry = ttk.Entry(price_frame, 
                                    textvariable=self.price_var, width=10)
        self.price_entry.pack(side=tk.LEFT, padx=5)
        self.price_button = ttk.Button(price_frame, text="設定價格",
                                     command=self._handle_price_change,
                                     state=tk.DISABLED)
        self.price_button.pack(side=tk.LEFT, padx=5)
        
        # Quantity Input
        quantity_frame = ttk.Frame(order_frame)
        quantity_frame.pack(fill=tk.X, pady=5)
        ttk.Label(quantity_frame, text="數量:").pack(side=tk.LEFT, padx=5)
        self.quantity_var = tk.StringVar(value="1")
        self.quantity_entry = ttk.Entry(quantity_frame, 
                                      textvariable=self.quantity_var, width=10)
        self.quantity_entry.pack(side=tk.LEFT, padx=5)
        self.quantity_button = ttk.Button(quantity_frame, text="更改數量",
                                        command=self._handle_quantity_change,
                                        state=tk.DISABLED)
        self.quantity_button.pack(side=tk.LEFT, padx=5)
        
        # Order Button Frame
        order_button_frame = ttk.Frame(order_frame)
        order_button_frame.pack(pady=5)
        
        # Test Mode Checkbox
        self.test_mode_var = tk.BooleanVar(value=True)
        self.test_mode_checkbox = ttk.Checkbutton(order_button_frame,
                                                    text="測試模式",
                                                    variable=self.test_mode_var)
        self.test_mode_checkbox.pack(side=tk.LEFT, padx=5)

        # Bracket Order Checkbox
        self.bracket_order_var = tk.BooleanVar(value=False)
        self.bracket_order_checkbox = ttk.Checkbutton(order_button_frame,
                                                        text="啟用Bracket Order",
                                                        variable=self.bracket_order_var)
        self.bracket_order_checkbox.pack(side=tk.LEFT, padx=5)

        # Order Button
        self.order_button = ttk.Button(order_button_frame, text="下單",
                                     command=self._handle_order,
                                     state=tk.DISABLED)
        self.order_button.pack(side=tk.LEFT, padx=5)
        
        # Flatten All Button (新增)
        self.flatten_all_button = ttk.Button(order_frame, text="平倉所有",
                                           command=self._handle_flatten_all,
                                           state=tk.DISABLED)
        self.flatten_all_button.pack(pady=5)
        
        # API Server Control Frame
        api_frame = ttk.Frame(main_frame, padding="5")
        api_frame.pack(fill=tk.X)
        self.api_button = ttk.Button(api_frame, text="啟動API", command=self._handle_api_toggle)
        self.api_button.pack(side=tk.LEFT, padx=5)
        self.api_status_label = ttk.Label(api_frame, text="API服務器: 未啟動")
        self.api_status_label.pack(side=tk.LEFT, padx=5)
        
        # Bottom Status Bar
        self.status_var = tk.StringVar(value="準備就緒")
        self.bottom_status = ttk.Label(self.root, textvariable=self.status_var,
                                     relief=tk.SUNKEN, padding=(5, 2))
        self.bottom_status.pack(side=tk.BOTTOM, fill=tk.X, before=main_frame)

    def _handle_order_type_change(self, event):
        """處理訂單類型變更"""
        order_type = self.order_type_var.get()
        try:
            if self.order_helper.select_order_type(order_type):
                self.status_var.set(f"已選擇訂單類型: {order_type}")
                # 根據訂單類型啟用/禁用價格輸入
                if order_type in ["Limit", "Stop Market"]:  # 修改這裡，加入Stop Market
                    self.price_button.config(state=tk.NORMAL)
                    self.price_entry.config(state=tk.NORMAL)
                else:
                    self.price_button.config(state=tk.DISABLED)
                    self.price_entry.config(state=tk.DISABLED)
            else:
                self.status_var.set("選擇訂單類型失敗")
        except Exception as e:
            self.status_var.set(f"選擇訂單類型時發生錯誤: {str(e)}")

    def _handle_order(self):
        """處理下單按鈕點擊"""
        def order_thread():
            self.order_button.config(state=tk.DISABLED)  # 禁用按鈕
            try:
                # 獲取方向
                direction = self.direction_var.get()
                
                # 獲取商品名稱
                product_name = self.product_entry.get().strip()
                if not product_name:
                    self.status_var.set("請輸入商品名稱")
                    return
                
                # 獲取訂單類型
                order_type = self.order_type_var.get()
                
                # 處理價格
                price = None
                if order_type in ["Limit", "Stop Market"]:
                    try:
                        price = float(self.price_var.get().strip())
                        if price <= 0:
                            self.status_var.set("價格必須大於0")
                            return
                    except ValueError:
                        self.status_var.set("請輸入有效的價格")
                        return
                
                # 處理數量
                try:
                    quantity = int(self.quantity_var.get().strip())
                    if quantity < 1:
                        self.status_var.set("數量必須大於0")
                        return
                except ValueError:
                    self.status_var.set("請輸入有效的數量")
                    return
                
                # 處理止損止盈
                sl = self.sl_var.get().strip() or "0"
                tp = self.tp_var.get().strip() or "0"
                
                # 執行下單流程
                success, message = self.order_helper.place_order(
                    product_name=product_name,
                    order_type=order_type,
                    quantity=quantity,
                    sl=sl,
                    tp=tp,
                    price=price,
                    test_mode=self.test_mode_var.get(),
                    direction=direction,
                    bracket_order=self.bracket_order_var.get()
                )
                
                self.status_var.set(message)
                
            except Exception as e:
                self.status_var.set(f"下單準備過程發生錯誤: {str(e)}")
            finally:
                self.order_button.config(state=tk.NORMAL)  # 重新啟用按鈕
        
        # 在新線程中執行下單操作
        threading.Thread(target=order_thread, daemon=True).start()

    def _handle_quantity_change(self):
        """處理更改數量按鈕點擊"""
        def quantity_thread():
            self.quantity_button.config(state=tk.DISABLED)  # 禁用按鈕
            try:
                quantity = self.quantity_var.get().strip()
                if not quantity:
                    self.status_var.set("請輸入數量")
                    return
                
                try:
                    quantity = int(quantity)
                    if quantity < 1:
                        self.status_var.set("數量必須大於0")
                        return
                except ValueError:
                    self.status_var.set("請輸入有效的數量")
                    return
                
                if self.order_helper.change_quantity(quantity):
                    self.status_var.set(f"已更改數量為: {quantity}")
                else:
                    self.status_var.set("更改數量失敗")
            except Exception as e:
                self.status_var.set(f"更改數量失敗: {str(e)}")
            finally:
                self.quantity_button.config(state=tk.NORMAL)  # 重新啟用按鈕
        
        # 在新線程中執行更改數量操作
        threading.Thread(target=quantity_thread, daemon=True).start()

    def update_status(self, message):
        """更新狀態欄"""
        # 確保在主線程中更新 GUI
        self.root.after(0, lambda: self.status_var.set(message))

    def update_detection(self, message, color):
        """更新檢測狀態"""
        self.detection_label.config(text=message, foreground=color)
        
        # 檢查當前URL是否為settings頁面
        current_url = self.current_url_label.cget("text")
        is_settings_page = "settings" in current_url.lower() if current_url else False
        
        # 如果是登入狀態或在設定頁面，啟用按鈕
        if (message == "已登入" and color == "green") or (message == "非交易頁面" and is_settings_page):
            self.order_button.config(state=tk.NORMAL)
            self.quantity_button.config(state=tk.NORMAL)
            self.logout_button.config(state=tk.NORMAL)
            self.settings_button.config(state=tk.NORMAL)
            self.sl_tp_button.config(state=tk.NORMAL)
            self.product_button.config(state=tk.NORMAL)
            self.flatten_all_button.config(state=tk.NORMAL)  # 啟用平倉所有按鈕
            # 根據當前訂單類型決定是否啟用價格按鈕
            if self.order_type_var.get() in ["Limit", "Stop Market"]:  # 修改這裡，加入Stop Market
                self.price_button.config(state=tk.NORMAL)
                self.price_entry.config(state=tk.NORMAL)
            else:
                self.price_button.config(state=tk.DISABLED)
                self.price_entry.config(state=tk.DISABLED)
            
            # 只在首次登入成功時同步數據
            if message == "已登入" and color == "green" and not self.initial_sync_done:
                def init_fields():
                    max_retries = 3
                    retry_delay = 2  # 重試延遲秒數
                    
                    for attempt in range(max_retries):
                        try:
                            sync_messages = []
                            sync_success = True
                            
                            # 等待頁面完全加載
                            time.sleep(retry_delay)
                            
                            # 獲取當前商品
                            if not self.product_synced:
                                current_product = self.order_helper.get_current_product()
                                if current_product:
                                    self.product_entry.delete(0, tk.END)
                                    self.product_entry.insert(0, current_product)
                                    self.product_synced = True
                                    sync_messages.append("商品")
                                else:
                                    sync_success = False
                            
                            # 獲取當前訂單類型
                            if not self.order_type_synced:
                                current_type = self.order_helper.get_current_order_type()
                                if current_type in ["Market", "Limit", "Stop Market"]:
                                    self.order_type_var.set(current_type)
                                    self.order_type_synced = True
                                    sync_messages.append("訂單類型")
                                else:
                                    sync_success = False
                            
                            # 獲取當前數量
                            if not self.quantity_synced:
                                current_quantity = self.order_helper.get_current_quantity()
                                if current_quantity:
                                    self.quantity_var.set(current_quantity)
                                    self.quantity_synced = True
                                    sync_messages.append("數量")
                                else:
                                    sync_success = False
                            
                            if sync_success:
                                if sync_messages:
                                    self.status_var.set(f"已同步: {', '.join(sync_messages)}")
                                    self.initial_sync_done = True  # 標記已完成初始同步
                                    return  # 如果成功則退出
                                elif attempt < max_retries - 1:  # 如果不是最後一次嘗試
                                    self.status_var.set(f"同步重試中... (第 {attempt + 1} 次)")
                                    continue
                        
                        except Exception as e:
                            if attempt < max_retries - 1:  # 如果不是最後一次嘗試
                                self.status_var.set(f"同步重試中... (第 {attempt + 1} 次)")
                                continue
                            else:
                                self.status_var.set(f"同步頁面數據時發生錯誤: {str(e)}")
                    
                    # 如果所有重試都失敗
                    if not self.initial_sync_done:
                        self.status_var.set("無法完成頁面同步，請手動設置參數")
                
                # 在新線程中執行初始化
                threading.Thread(target=init_fields, daemon=True).start()
        else:
            self.order_button.config(state=tk.DISABLED)
            self.quantity_button.config(state=tk.DISABLED)
            self.logout_button.config(state=tk.DISABLED)
            self.settings_button.config(state=tk.DISABLED)
            self.sl_tp_button.config(state=tk.DISABLED)
            self.product_button.config(state=tk.DISABLED)
            self.price_button.config(state=tk.DISABLED)
            self.price_entry.config(state=tk.DISABLED)
            self.flatten_all_button.config(state=tk.DISABLED)  # 禁用平倉所有按鈕

    def update_url(self, url):
        """更新URL顯示"""
        self.current_url_label.config(text=url)

    def update_gui_fields(self, product_name=None, order_type=None, quantity=None, 
                         sl=None, tp=None, price=None, direction=None):
        """更新GUI輸入框的值"""
        def _update():
            if product_name is not None:
                self.product_entry.delete(0, tk.END)
                self.product_entry.insert(0, product_name)
            
            if order_type is not None:
                self.order_type_var.set(order_type)
                # 觸發訂單類型變更事件
                self._handle_order_type_change(None)
            
            if quantity is not None:
                self.quantity_var.set(str(quantity))
            
            if sl is not None:
                self.sl_var.set(str(sl))
            
            if tp is not None:
                self.tp_var.set(str(tp))
            
            if price is not None:
                self.price_var.set(str(price))
            
            if direction is not None:
                self.direction_var.set(direction)

        # 在主線程中執行更新
        self.root.after(0, _update)

    def start(self):
        """啟動監控"""
        self.login_helper = LoginHelper()
        self.login_helper.set_callbacks(
            status_callback=self.update_status,
            detection_callback=self.update_detection,
            url_callback=self.update_url
        )
        
        # 初始化下單助手
        self.order_helper = OrderHelper(
            driver=self.login_helper.driver,
            selenium_helper=self.login_helper.selenium_helper
        )
        self.order_helper.set_status_callback(self.update_status)
        
        # 初始化API服務器，並傳入更新GUI方法
        self.api_server = APIServer(
            order_helper=self.order_helper,
            gui_update_callback=self.update_gui_fields
        )
        
        # 啟動監控線程
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        # 啟動瀏覽器和登入
        self.browser_thread = threading.Thread(target=self._browser_process)
        self.browser_thread.daemon = True
        self.browser_thread.start()

    def _browser_process(self):
        """瀏覽器處理線程"""
        if not self.is_closing and self.login_helper.start_browser():
            if not self.is_closing:
                self.login_helper.auto_login()
                # 更新 order_helper 的 driver 和 selenium_helper
                self.order_helper.driver = self.login_helper.driver
                self.order_helper.selenium_helper = self.login_helper.selenium_helper

    def _monitoring_loop(self):
        """監控循環"""
        while self.is_monitoring and not self.is_closing:
            if not self.is_closing:
                self.login_helper.monitor_status()
                time.sleep(0.5)

    def on_closing(self):
        """關閉程序"""
        try:
            self.is_closing = True
            self.is_monitoring = False
            
            self.status_var.set("正在關閉程式...")
            self.root.update()
            
            # 停止API服務器
            if hasattr(self, 'api_server'):
                self.api_server.stop()
            
            if hasattr(self, 'login_helper'):
                self.login_helper.close()
            
            self.root.quit()
            self.root.destroy()
        except Exception as e:
            print(f"Error during closing: {str(e)}")
            import os
            os._exit(0)

    def run(self):
        """運行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def _handle_logout(self):
        """處理登出按鈕點擊"""
        def logout_thread():
            self.logout_button.config(state=tk.DISABLED)  # 禁用按鈕
            try:
                if self.login_helper.perform_logout():
                    self.status_var.set("已成功登出")
                    # 禁用所有操作按鈕
                    self.order_button.config(state=tk.DISABLED)
                    self.quantity_button.config(state=tk.DISABLED)
                else:
                    self.status_var.set("登出失敗")
                    self.logout_button.config(state=tk.NORMAL)  # 重新啟用登出按鈕
            except Exception as e:
                self.status_var.set(f"登出操作失敗: {str(e)}")
                self.logout_button.config(state=tk.NORMAL)  # 重新啟用登出按鈕
        
        # 在新線程中執行登出操作
        threading.Thread(target=logout_thread, daemon=True).start()

    def _handle_settings(self):
        """處理設定按鈕點擊"""
        def settings_thread():
            try:
                # 使用 SeleniumHelper 獲取設定按鈕
                settings_button, error = self.login_helper.selenium_helper.get_operation_button('Settings')
                if not settings_button:
                    self.status_var.set(f"找不到設定按鈕: {error}")
                    return
                
                # 點擊設定按鈕
                settings_button.click()
                self.status_var.set("已點擊設定按鈕")
                
                # 等待一下確保選單出現
                time.sleep(0.5)
                
                # 找到並點擊 Risk Settings
                risk_settings, error = self.login_helper.selenium_helper.find_div_by_text('Risk Settings')
                if risk_settings:
                    risk_settings.click()
                    self.status_var.set("已點擊 Risk Settings")
                else:
                    self.status_var.set(f"找不到 Risk Settings: {error}")
                
            except Exception as e:
                self.status_var.set(f"設定操作失敗: {str(e)}")
        
        # 在新線程中執行設定操作
        threading.Thread(target=settings_thread, daemon=True).start()

    def _handle_sl_tp(self):
        """處理止損止盈設定"""
        def sl_tp_thread():
            self.sl_tp_button.config(state=tk.DISABLED)  # 禁用按鈕
            try:
                # 首先點擊設定按鈕並進入Risk Settings
                settings_button, error = self.login_helper.selenium_helper.get_operation_button('Settings')
                if not settings_button:
                    self.status_var.set(f"找不到設定按鈕: {error}")
                    return
                
                # 點擊設定按鈕
                settings_button.click()
                self.status_var.set("已點擊設定按鈕")
                
                # 等待確保選單出現
                time.sleep(0.5)
                
                # 找到並點擊 Risk Settings
                risk_settings, error = self.login_helper.selenium_helper.find_div_by_text('Risk Settings')
                if not risk_settings:
                    self.status_var.set(f"找不到 Risk Settings: {error}")
                    return
                
                risk_settings.click()
                self.status_var.set("已進入風險設定頁面")
                
                # 等待頁面加載
                time.sleep(1)
                
                # 獲取輸入值，如果為空則設為 "0"
                sl = self.sl_var.get().strip() or "0"
                tp = self.tp_var.get().strip() or "0"
                
                # 找到輸入框
                sl_input, tp_input, error = self.login_helper.selenium_helper.find_sl_tp_inputs()
                if error:
                    self.status_var.set(error)
                    return
                
                success = False
                sl_msg = ""
                tp_msg = ""
                
                # 設置止損
                if sl_input:
                    try:
                        sl_input.clear()
                        sl_input.send_keys(sl)
                        success = True
                        sl_msg = f"止損={sl}"
                    except Exception as e:
                        self.status_var.set(f"設置止損失敗: {str(e)}")
                        return
                
                # 設置止盈
                if tp_input:
                    try:
                        tp_input.clear()
                        tp_input.send_keys(tp)
                        success = True
                        tp_msg = f"止盈={tp}"
                    except Exception as e:
                        self.status_var.set(f"設置止盈失敗: {str(e)}")
                        return
                
                if success:
                    # 尋找並點擊Save按鈕
                    save_button, error = self.login_helper.selenium_helper.find_button_by_text('Save')
                    if save_button:
                        try:
                            save_button.click()
                            # 組合消息
                            msg_parts = []
                            if sl_msg: msg_parts.append(sl_msg)
                            if tp_msg: msg_parts.append(tp_msg)
                            save_message = "已保存設定: " + ", ".join(msg_parts)
                            self.status_var.set(save_message)
                            
                            # 等待保存完成
                            time.sleep(1)
                            
                            # 只有在成功保存後才返回交易頁面
                            trading_button, error = self.login_helper.selenium_helper.get_operation_button('Trading')
                            if trading_button:
                                trading_button.click()
                                self.status_var.set(f"{save_message}，已返回交易頁面")
                            else:
                                self.status_var.set(f"{save_message}，但無法返回交易頁面: {error}")
                                
                        except Exception as e:
                            self.status_var.set(f"保存設定失敗: {str(e)}")
                    else:
                        self.status_var.set(f"找不到保存按鈕: {error}")
                
            except Exception as e:
                self.status_var.set(f"設置止損止盈失敗: {str(e)}")
            finally:
                self.sl_tp_button.config(state=tk.NORMAL)  # 重新啟用按鈕
        
        # 在新線程中執行設定操作
        threading.Thread(target=sl_tp_thread, daemon=True).start()

    def _handle_product_change(self):
        """處理切換商品按鈕點擊"""
        def product_thread():
            self.product_button.config(state=tk.DISABLED)  # 禁用按鈕
            try:
                product_name = self.product_entry.get().strip()
                if not product_name:
                    self.status_var.set("請輸入商品名稱")
                    return
                
                # 嘗試切換商品
                max_retries = 3
                for attempt in range(max_retries):
                    if self.order_helper.click_contract_input(product_name):
                        self.status_var.set(f"已切換商品: {product_name}")
                        return
                    elif attempt < max_retries - 1:  # 如果不是最後一次嘗試
                        time.sleep(1)  # 等待1秒後重試
                        self.status_var.set(f"切換商品重試中... (第 {attempt + 1} 次)")
                
                # 如果所有重試都失敗
                self.status_var.set("切換商品失敗，請重試")
                
            except Exception as e:
                self.status_var.set(f"切換商品失敗: {str(e)}")
            finally:
                self.product_button.config(state=tk.NORMAL)  # 重新啟用按鈕
        
        # 在新線程中執行切換商品操作
        threading.Thread(target=product_thread, daemon=True).start()

    def _handle_price_change(self):
        """處理價格變更按鈕點擊"""
        def price_thread():
            self.price_button.config(state=tk.DISABLED)  # 禁用按鈕
            try:
                price = self.price_var.get().strip()
                if not price:
                    self.status_var.set("請輸入價格")
                    return
                
                try:
                    price = float(price)
                    if price <= 0:
                        self.status_var.set("價格必須大於0")
                        return
                except ValueError:
                    self.status_var.set("請輸入有效的價格")
                    return
                
                # 找到價格輸入框並設置值
                limit_price_input = self.login_helper.selenium_helper.find_limit_price_input()
                if limit_price_input:
                    try:
                        # 點擊輸入框以確保焦點
                        limit_price_input.click()
                        time.sleep(0.1)
                        
                        # 全選當前內容
                        limit_price_input.send_keys(Keys.CONTROL, 'a')
                        time.sleep(0.1)
                        
                        # 輸入新價格
                        limit_price_input.send_keys(str(price))
                        time.sleep(0.1)
                        
                        # 按下 Enter 確認輸入
                        limit_price_input.send_keys(Keys.ENTER)
                        
                        self.status_var.set(f"已設定價格為: {price}")
                    except Exception as e:
                        self.status_var.set(f"設定價格失敗: {str(e)}")
                else:
                    self.status_var.set("找不到價格輸入框")
                
            except Exception as e:
                self.status_var.set(f"設定價格失敗: {str(e)}")
            finally:
                if self.order_type_var.get() in ["Limit", "Stop Market"]:  # 修改這裡，加入Stop Market
                    self.price_button.config(state=tk.NORMAL)  # 只有在需要價格的模式下才重新啟用按鈕
        
        # 在新線程中執行價格變更操作
        threading.Thread(target=price_thread, daemon=True).start()

    def _handle_flatten_all(self):
        """處理平倉所有按鈕點擊"""
        def flatten_thread():
            self.flatten_all_button.config(state=tk.DISABLED)  # 禁用按鈕
            try:
                # 尋找 Flatten All 按鈕
                flatten_button = WebDriverWait(self.login_helper.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, 
                        "//button[contains(., 'Flatten All')]"))
                )
                
                if flatten_button:
                    flatten_button.click()
                    self.status_var.set("已執行平倉所有")
                else:
                    self.status_var.set("找不到平倉所有按鈕")
                    
            except Exception as e:
                self.status_var.set(f"平倉所有操作失敗: {str(e)}")
            finally:
                self.flatten_all_button.config(state=tk.NORMAL)  # 重新啟用按鈕
        
        # 在新線程中執行平倉操作
        threading.Thread(target=flatten_thread, daemon=True).start()

    def _handle_api_toggle(self):
        """處理API服務器開關"""
        def api_thread():
            try:
                if not self.api_server.is_running:
                    # 嘗試啟動API服務器
                    if self.api_server.start():
                        self.api_status_label.config(text="API服務器: 運行中 (端口: 8168)")
                        self.api_button.config(text="停止API")
                        self.status_var.set("API服務器已啟動")
                    else:
                        self.status_var.set("API服務器啟動失敗")
                else:
                    # 嘗試停止API服務器
                    if self.api_server.stop():
                        self.api_status_label.config(text="API服務器: 未啟動")
                        self.api_button.config(text="啟動API")
                        self.status_var.set("API服務器已停止")
                    else:
                        self.status_var.set("API服務器停止失敗")
            except Exception as e:
                self.status_var.set(f"API服務器操作失敗: {str(e)}")
        
        # 在新線程中執行API服務器操作
        threading.Thread(target=api_thread, daemon=True).start()

def main():
    app = MonitorGUI()
    app.run()

if __name__ == "__main__":
    main() 