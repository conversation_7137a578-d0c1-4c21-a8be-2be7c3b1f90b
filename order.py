from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import time

class OrderHelper:
    def __init__(self, driver=None, selenium_helper=None):
        self.driver = driver
        self.selenium_helper = selenium_helper
        self._status_callback = None
        
    def set_status_callback(self, callback):
        """設置狀態更新回調函數"""
        self._status_callback = callback
        
    def _update_status(self, message):
        """更新狀態信息"""
        if self._status_callback:
            self._status_callback(message)

    def set_selenium_helper(self, selenium_helper):
        """設置 selenium helper"""
        self.selenium_helper = selenium_helper

    def click_contract_input(self, product_name):
        """點擊合約輸入框並輸入商品名稱"""
        try:
            self._update_status("尋找下單輸入框...")
            
            # 使用更精確的選擇器找到 MuiAutocomplete 輸入框
            input_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 
                    "#orderCardTab .MuiAutocomplete-root input[type='text']"))
            )
            
            # 獲取當前輸入框的值
            current_value = input_field.get_attribute('value').strip().lower()
            product_name_lower = product_name.lower()
            
            # 檢查當前值是否與要輸入的商品名稱匹配
            if current_value.startswith(product_name_lower):
                self._update_status(f"當前輸入框已包含目標商品: {current_value}")
                return True
            
            # 點擊輸入框
            input_field.click()
            time.sleep(0.5)  # 等待點擊生效
            
            # 使用 JavaScript 清空輸入框
            self.driver.execute_script("arguments[0].value = '';", input_field)
            time.sleep(0.5)  # 等待清空生效
            
            # 輸入商品名稱
            input_field.send_keys(product_name)
            time.sleep(0.5)  # 等待輸入生效
            
            try:
                # 等待下拉列表出現
                listbox = WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        "ul[role='listbox']"))
                )
                
                # 獲取所有選項
                options = listbox.find_elements(By.CSS_SELECTOR, "li[role='option']")
                
                # 打印所有可用選項，用於診斷
                print(f"可用選項: {[opt.text.strip() for opt in options]}")
                
                # 尋找最佳匹配選項
                best_match = None
                
                # 1. 首先尋找完全匹配
                for option in options:
                    if option.text.strip().lower() == product_name_lower:
                        best_match = option
                        print(f"找到完全匹配: {option.text}")
                        break
                
                # 2. 如果沒有完全匹配，尋找以輸入文字開頭的選項
                if not best_match:
                    starts_with_matches = [opt for opt in options 
                                        if opt.text.strip().lower().startswith(product_name_lower)]
                    if starts_with_matches:
                        best_match = min(starts_with_matches, key=lambda x: len(x.text))
                        print(f"找到前綴匹配: {best_match.text}")
                
                # 3. 如果還沒找到，才尋找包含輸入文字的選項
                if not best_match:
                    contains_matches = [opt for opt in options 
                                     if product_name_lower in opt.text.strip().lower()]
                    if contains_matches:
                        best_match = min(contains_matches, key=lambda x: len(x.text))
                        print(f"找到包含匹配: {best_match.text}")
                
                if not best_match:
                    self._update_status("未找到匹配的商品選項")
                    return False
                
                # 點擊最佳匹配選項
                selected_text = best_match.text.strip()
                print(f"選擇選項: {selected_text}")
                best_match.click()
                
                # 等待並驗證選擇是否生效
                max_attempts = 3
                for attempt in range(max_attempts):
                    time.sleep(1)
                    final_value = input_field.get_attribute('value').strip()
                    print(f"第 {attempt + 1} 次檢查 - 最終值: '{final_value}', 期望值: '{selected_text}'")
                    
                    if final_value == selected_text:
                        self._update_status(f"已成功切換到商品: {final_value}")
                        return True
                    
                    # 如果值不匹配但不是空的，可能是選擇已經生效但顯示不同
                    if final_value and (final_value.lower().startswith(product_name_lower) or 
                                      product_name_lower in final_value.lower()):
                        self._update_status(f"已切換到相關商品: {final_value}")
                        return True
                
                self._update_status(f"商品切換驗證失敗 - 最終值: {final_value}")
                return False
                    
            except Exception as e:
                self._update_status(f"選擇商品失敗: {str(e)}")
                print(f"Error selecting product: {str(e)}")
                return False
            
        except Exception as e:
            self._update_status(f"輸入商品失敗: {str(e)}")
            print(f"Error inputting product: {str(e)}")
            return False

    def select_order_type(self, order_type):
        """選擇訂單類型"""
        try:
            self._update_status("尋找訂單類型選擇框...")
            
            # 找到並點擊訂單類型選擇框
            order_type_div = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 
                    "#orderCardTab div[role='combobox']"))
            )
            
            # 檢查當前選擇的訂單類型
            current_type = order_type_div.text.strip()
            if current_type == order_type:
                self._update_status(f"當前已是 {order_type} 訂單類型")
                return True
            
            # 點擊打開選單
            order_type_div.click()
            time.sleep(0.5)
            
            # 等待選單出現並選擇指定類型
            try:
                # 尋找選項
                option = WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, 
                        f"//li[contains(@class, 'MuiMenuItem-root') and text()='{order_type}']"))
                )
                option.click()
                self._update_status(f"已選擇訂單類型: {order_type}")
                return True
                
            except Exception as e:
                self._update_status(f"選擇訂單類型失敗: {str(e)}")
                print(f"Error selecting order type: {str(e)}")
                return False
            
        except Exception as e:
            self._update_status(f"找不到訂單類型選擇框: {str(e)}")
            print(f"Error finding order type selector: {str(e)}")
            return False

    def change_quantity(self, quantity):
        """更改訂單數量"""
        try:
            self._update_status("尋找數量輸入框...")
            
            # 找到數量輸入框
            quantity_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 
                    "#orderCardTab input[type='number'][min='1']"))
            )
            
            # 獲取當前數量
            current_quantity = quantity_input.get_attribute('value')
            if current_quantity == str(quantity):
                self._update_status(f"當前數量與設定值相同: {quantity}，重新輸入以確保設置...")
            
            # 點擊輸入框以確保焦點
            quantity_input.click()
            time.sleep(0.1)
            
            # 全選當前內容
            quantity_input.send_keys(Keys.CONTROL, 'a')
            time.sleep(0.1)
            
            # 輸入新數量
            quantity_input.send_keys(str(quantity))
            time.sleep(0.1)
            
            # 按下 Enter 確認輸入
            quantity_input.send_keys(Keys.ENTER)
            
            self._update_status(f"已更改數量為: {quantity}")
            return True
            
        except Exception as e:
            self._update_status(f"更改數量失敗: {str(e)}")
            print(f"Error changing quantity: {str(e)}")
            return False 

    def get_current_product(self):
        """獲取當前商品名稱"""
        max_retries = 3
        retry_delay = 1  # 重試延遲秒數
        
        for attempt in range(max_retries):
            try:
                # 等待輸入框出現
                input_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        "#orderCardTab .MuiAutocomplete-root input[type='text']"))
                )
                
                # 等待頁面加載完成
                time.sleep(retry_delay)
                
                # 獲取值
                value = input_field.get_attribute('value').strip()
                if value:  # 如果成功獲取到值
                    return value
                elif attempt < max_retries - 1:  # 如果沒有值且不是最後一次嘗試
                    time.sleep(retry_delay)  # 等待後重試
                    continue
                    
            except Exception as e:
                if attempt < max_retries - 1:  # 如果不是最後一次嘗試
                    print(f"嘗試獲取商品名稱失敗 (第 {attempt + 1} 次): {str(e)}")
                    time.sleep(retry_delay)  # 等待後重試
                    continue
                else:
                    print(f"Error getting current product: {str(e)}")
        
        return ""  # 如果所有嘗試都失敗，返回空字符串

    def get_current_order_type(self):
        """獲取當前訂單類型"""
        max_retries = 3
        retry_delay = 1  # 重試延遲秒數
        
        for attempt in range(max_retries):
            try:
                # 等待訂單類型選擇框出現
                order_type_div = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        "#orderCardTab div[role='combobox']"))
                )
                
                # 等待頁面加載完成
                time.sleep(retry_delay)
                
                # 獲取值
                value = order_type_div.text.strip()
                if value in ["Market", "Limit", "Stop Market"]:  # 如果是有效的訂單類型
                    return value
                elif attempt < max_retries - 1:  # 如果無效且不是最後一次嘗試
                    time.sleep(retry_delay)  # 等待後重試
                    continue
                    
            except Exception as e:
                if attempt < max_retries - 1:  # 如果不是最後一次嘗試
                    print(f"嘗試獲取訂單類型失敗 (第 {attempt + 1} 次): {str(e)}")
                    time.sleep(retry_delay)  # 等待後重試
                    continue
                else:
                    print(f"Error getting current order type: {str(e)}")
        
        return "Market"  # 如果所有嘗試都失敗，返回默認值

    def get_current_quantity(self):
        """獲取當前數量"""
        max_retries = 3
        retry_delay = 1  # 重試延遲秒數
        
        for attempt in range(max_retries):
            try:
                # 等待數量輸入框出現
                quantity_input = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        "#orderCardTab input[type='number'][min='1']"))
                )
                
                # 等待頁面加載完成
                time.sleep(retry_delay)
                
                # 獲取值
                value = quantity_input.get_attribute('value')
                if value:  # 如果成功獲取到值
                    return value
                elif attempt < max_retries - 1:  # 如果沒有值且不是最後一次嘗試
                    time.sleep(retry_delay)  # 等待後重試
                    continue
                    
            except Exception as e:
                if attempt < max_retries - 1:  # 如果不是最後一次嘗試
                    print(f"嘗試獲取數量失敗 (第 {attempt + 1} 次): {str(e)}")
                    time.sleep(retry_delay)  # 等待後重試
                    continue
                else:
                    print(f"Error getting current quantity: {str(e)}")

        return "1"  # 如果所有嘗試都失敗，返回默認值

    def get_current_price(self):
        """獲取當前市場價格"""
        max_retries = 3
        retry_delay = 1

        for attempt in range(max_retries):
            try:
                # 嘗試多種可能的價格顯示元素
                price_selectors = [
                    "span[data-testid='last-price']",  # 常見的最新價格
                    ".price-display",
                    "[class*='price']",
                    "[class*='last']",
                    "span:contains('$')",
                ]

                for selector in price_selectors:
                    try:
                        price_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in price_elements:
                            price_text = element.text.strip()
                            # 嘗試提取數字
                            import re
                            price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
                            if price_match:
                                price_str = price_match.group().replace(',', '')
                                try:
                                    price = float(price_str)
                                    if price > 0:  # 確保是有效價格
                                        return price
                                except ValueError:
                                    continue
                    except Exception:
                        continue

                # 如果上述方法都失敗，嘗試從頁面標題或其他地方獲取
                try:
                    page_text = self.driver.find_element(By.TAG_NAME, "body").text
                    import re
                    # 尋找類似價格的模式
                    price_patterns = [
                        r'\$?([\d,]+\.?\d+)',
                        r'([\d,]+\.?\d+)\s*USD',
                        r'Price[:\s]*([\d,]+\.?\d+)',
                    ]

                    for pattern in price_patterns:
                        matches = re.findall(pattern, page_text)
                        for match in matches:
                            try:
                                price = float(match.replace(',', ''))
                                if 1 < price < 1000000:  # 合理的價格範圍
                                    return price
                            except ValueError:
                                continue

                except Exception:
                    pass

                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue

            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"嘗試獲取當前價格失敗 (第 {attempt + 1} 次): {str(e)}")
                    time.sleep(retry_delay)
                    continue
                else:
                    print(f"Error getting current price: {str(e)}")

        return None  # 如果所有嘗試都失敗，返回None

    def _validate_sl_tp_prices(self, direction, current_price, sl, tp):
        """
        驗證止損止盈價格的合理性

        Args:
            direction (str): 交易方向 (Buy/Sell)
            current_price (float): 當前市場價格
            sl (str): 止損價格
            tp (str): 止盈價格

        Returns:
            tuple: (bool, str) - (是否有效, 錯誤消息)
        """
        try:
            # 轉換價格為浮點數
            sl_price = float(sl) if sl and sl != "0" else None
            tp_price = float(tp) if tp and tp != "0" else None

            if direction == "Buy":  # 做多
                # 做多時：止損應該在當前價格下方，止盈應該在當前價格上方
                if sl_price is not None:
                    if sl_price >= current_price:
                        return False, f"做多止損價格({sl_price})應該低於當前價格({current_price})"

                if tp_price is not None:
                    if tp_price <= current_price:
                        return False, f"做多止盈價格({tp_price})應該高於當前價格({current_price})"

            elif direction == "Sell":  # 做空
                # 做空時：止損應該在當前價格上方，止盈應該在當前價格下方
                if sl_price is not None:
                    if sl_price <= current_price:
                        return False, f"做空止損價格({sl_price})應該高於當前價格({current_price})"

                if tp_price is not None:
                    if tp_price >= current_price:
                        return False, f"做空止盈價格({tp_price})應該低於當前價格({current_price})"

            # 檢查止損止盈價格的邏輯關係
            if sl_price is not None and tp_price is not None:
                if direction == "Buy":
                    if sl_price >= tp_price:
                        return False, f"做多時止損價格({sl_price})應該低於止盈價格({tp_price})"
                elif direction == "Sell":
                    if sl_price <= tp_price:
                        return False, f"做空時止損價格({sl_price})應該高於止盈價格({tp_price})"

            return True, "價格驗證通過"

        except ValueError as e:
            return False, f"價格格式錯誤: {str(e)}"
        except Exception as e:
            return False, f"價格驗證失敗: {str(e)}"

    def place_order(self, product_name, order_type, quantity, sl="0", tp="0", price=None, test_mode=True, direction="Buy", bracket_order=False):
        """
        執行下單流程

        Args:
            product_name (str): 商品名稱
            order_type (str): 訂單類型 (Market/Limit/Stop Market)
            quantity (int): 數量（正整數）
            sl (str): 止損值，默認為"0"
            tp (str): 止盈值，默認為"0"
            price (float, optional): 限價單的價格，默認為None
            test_mode (bool): 是否為測試模式，默認為True
            direction (str): 交易方向 (Buy/Sell)，默認為"Buy"
            bracket_order (bool): 是否啟用Bracket Order，默認為False

        Returns:
            tuple: (bool, str) - (是否成功, 狀態消息)
        """
        try:
            # 檢查是否有設置 selenium_helper
            if not self.selenium_helper:
                return False, "selenium_helper 未設置"
                
            # 參數驗證
            if not product_name or not product_name.strip():
                return False, "商品名稱不能為空"
            
            if not order_type or order_type not in ["Market", "Limit", "Stop Market"]:
                return False, "無效的訂單類型"
            
            # 檢查非市價單是否有提供價格
            if order_type != "Market":
                if price is None:
                    return False, f"{order_type} 訂單必須指定價格"
                if not isinstance(price, (int, float)) or price <= 0:
                    return False, "價格必須是大於0的數字"
            
            if not isinstance(quantity, int) or quantity < 1:
                return False, "數量必須是大於0的整數"
            
            if direction not in ["Buy", "Sell"]:
                return False, "無效的交易方向，必須是 Buy 或 Sell"
            
            # 處理空白值
            sl = sl.strip() if isinstance(sl, str) else str(sl)
            tp = tp.strip() if isinstance(tp, str) else str(tp)
            sl = "0" if sl == "" else sl
            tp = "0" if tp == "" else tp
            
            # 步驟1: 設定止損和止盈 (只有在非Bracket Order模式下才需要)
            if not bracket_order:
                self._update_status("設置止損止盈...")

                settings_button, error = self.selenium_helper.get_operation_button('Settings')
                if not settings_button:
                    return False, f"找不到設定按鈕: {error}"

                settings_button.click()
                time.sleep(0.5)

                risk_settings, error = self.selenium_helper.find_div_by_text('Risk Settings')
                if not risk_settings:
                    return False, f"找不到 Risk Settings: {error}"

                risk_settings.click()
                time.sleep(1)

                sl_input, tp_input, error = self.selenium_helper.find_sl_tp_inputs()
                if error:
                    return False, error

                if sl_input and tp_input:
                    try:
                        # 獲取當前值
                        current_sl = sl_input.get_attribute('value') or "0"  # 空值視為0
                        current_tp = tp_input.get_attribute('value') or "0"  # 空值視為0

                        # 檢查是否需要修改
                        need_modification = current_sl != sl or current_tp != tp

                        if need_modification:
                            self._update_status(f"設置止損止盈: {current_sl}/{current_tp} -> {sl}/{tp}")
                            sl_input.clear()
                            sl_input.send_keys(sl)
                            tp_input.clear()
                            tp_input.send_keys(tp)

                            # 檢查Save按鈕是否出現
                            time.sleep(0.5)  # 等待Save按鈕可能出現
                            save_buttons = self.driver.find_elements(By.XPATH, "//button[contains(., 'Save')]")

                            if save_buttons:
                                save_buttons[0].click()
                                time.sleep(1)
                                self._update_status(f"已設置止損={sl}，止盈={tp}")
                            else:
                                self._update_status(f"止損止盈已設置為: 止損={sl}，止盈={tp}，但未找到Save按鈕")
                        else:
                            self._update_status(f"止損止盈已經是設定值: 止損={sl}，止盈={tp}")

                        trading_button, error = self.selenium_helper.get_operation_button('Trading')
                        if not trading_button:
                            return False, f"無法返回交易頁面: {error}"

                        trading_button.click()
                        time.sleep(1)

                    except Exception as e:
                        return False, f"設置止損止盈失敗: {str(e)}"
            else:
                self._update_status("Bracket Order模式: 跳過Settings中的Risk設定")
            
            # 步驟2: 切換商品
            if not self.click_contract_input(product_name):
                return False, "切換商品失敗"
            time.sleep(0.5)
            
            # 步驟3: 修改訂單類型
            if not self.select_order_type(order_type):
                return False, "設定訂單類型失敗"
            time.sleep(0.5)
            
            # 步驟4: 如果是非市價單，設定價格
            if order_type != "Market":
                limit_price_input = self.selenium_helper.find_limit_price_input()
                if not limit_price_input:
                    return False, "找不到價格輸入框"
                
                try:
                    # 點擊輸入框以確保焦點
                    limit_price_input.click()
                    time.sleep(0.1)
                    
                    # 全選當前內容
                    limit_price_input.send_keys(Keys.CONTROL, 'a')
                    time.sleep(0.1)
                    
                    # 輸入新價格
                    limit_price_input.send_keys(str(price))
                    time.sleep(0.1)
                    
                    # 按下 Enter 確認輸入
                    limit_price_input.send_keys(Keys.ENTER)
                    time.sleep(0.5)
                except Exception as e:
                    return False, f"設定價格失敗: {str(e)}"
            
            # 步驟5: 修改數量
            if not self.change_quantity(quantity):
                return False, "設定數量失敗"
            time.sleep(0.5)
            
            # 如果不是測試模式，執行實際下單
            if not test_mode:
                if bracket_order:
                    # Bracket Order 模式：下單多個訂單
                    return self._execute_bracket_order(direction, sl, tp, order_type, price)
                else:
                    # 普通下單模式
                    try:
                        # 根據direction判斷買入還是賣出
                        button_text = direction  # 直接使用direction作為按鈕文字

                        # 尋找下單按鈕
                        order_button = WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.XPATH,
                                f"//button[contains(., '{button_text}')]"))
                        )

                        if order_button:
                            order_button.click()
                            time.sleep(0.5)
                            return True, f"已執行{button_text}下單"
                        else:
                            return False, f"找不到{button_text}按鈕"

                    except Exception as e:
                        return False, f"執行下單失敗: {str(e)}"
            
            # 生成下單摘要
            order_summary = f"下單準備完成: 方向={direction}, 商品={product_name}, 類型={order_type}, " \
                          f"數量={quantity}"
            if price is not None:
                order_summary += f", 價格={price}"

            if bracket_order:
                order_summary += f", 止損={sl}, 止盈={tp} (Bracket Order模式 - 獨立訂單)"
            else:
                order_summary += f", 止損={sl}, 止盈={tp} (Account Risk設定)"

            if test_mode:
                order_summary += " (測試模式)"

            return True, order_summary
            
        except Exception as e:
            return False, f"下單準備過程發生錯誤: {str(e)}"

    def _execute_bracket_order(self, direction, sl, tp, main_order_type, main_price=None):
        """
        執行Bracket Order下單 - 分三個步驟獨立下單

        Args:
            direction (str): 主要訂單交易方向 (Buy/Sell)
            sl (str): 止損價格
            tp (str): 止盈價格
            main_order_type (str): 主要訂單類型
            main_price (float): 主要訂單價格 (如果是限價單)

        Returns:
            tuple: (bool, str) - (是否成功, 狀態消息)
        """
        try:
            orders_placed = []
            opposite_direction = "Sell" if direction == "Buy" else "Buy"

            # 獲取當前市場價格進行驗證
            self._update_status("獲取當前市場價格...")
            current_price = self.get_current_price()
            if current_price is None:
                return False, "無法獲取當前市場價格，無法驗證止損止盈設定"

            self._update_status(f"當前市場價格: {current_price}")

            # 驗證止損止盈價格的合理性
            validation_result = self._validate_sl_tp_prices(direction, current_price, sl, tp)
            if not validation_result[0]:
                return False, validation_result[1]

            # 第一步：下主要訂單
            self._update_status("Bracket Order 步驟1: 下主要訂單...")
            main_result = self._place_order_with_setup(direction, main_order_type, main_price)
            if main_result[0]:
                orders_placed.append(f"主要訂單({direction} {main_order_type})")
            else:
                return False, f"主要訂單失敗: {main_result[1]}"

            time.sleep(2)  # 等待主要訂單完成

            # 第二步：下止損訂單 (Stop Market，如果有設定SL且不為0)
            if sl and sl != "0":
                self._update_status("Bracket Order 步驟2: 下止損訂單...")
                sl_result = self._place_order_with_setup(opposite_direction, "Stop Market", float(sl))
                if sl_result[0]:
                    orders_placed.append(f"止損訂單({opposite_direction} Stop@{sl})")
                else:
                    return False, f"止損訂單失敗: {sl_result[1]}"

                time.sleep(2)  # 等待止損訂單完成

            # 第三步：下止盈訂單 (Limit，如果有設定TP且不為0)
            if tp and tp != "0":
                self._update_status("Bracket Order 步驟3: 下止盈訂單...")
                tp_result = self._place_order_with_setup(opposite_direction, "Limit", float(tp))
                if tp_result[0]:
                    orders_placed.append(f"止盈訂單({opposite_direction} Limit@{tp})")
                else:
                    return False, f"止盈訂單失敗: {tp_result[1]}"

            # 生成成功消息
            orders_count = len(orders_placed)
            orders_list = "、".join(orders_placed)
            success_message = f"Bracket Order完成: 已下{orders_count}個訂單 ({orders_list})"

            return True, success_message

        except Exception as e:
            return False, f"Bracket Order執行失敗: {str(e)}"

    def _place_order_with_setup(self, direction, order_type, price=None):
        """
        完整設置並下單一個訂單

        Args:
            direction (str): 交易方向 (Buy/Sell)
            order_type (str): 訂單類型 (Market/Limit/Stop Market)
            price (float): 價格 (限價單和止損單需要)

        Returns:
            tuple: (bool, str) - (是否成功, 狀態消息)
        """
        try:
            # 步驟1: 設置訂單類型
            if not self.select_order_type(order_type):
                return False, f"設定訂單類型 {order_type} 失敗"
            time.sleep(0.5)

            # 步驟2: 設置價格 (如果需要)
            if order_type != "Market" and price is not None:
                limit_price_input = self.selenium_helper.find_limit_price_input()
                if not limit_price_input:
                    return False, "找不到價格輸入框"

                try:
                    # 點擊輸入框以確保焦點
                    limit_price_input.click()
                    time.sleep(0.1)

                    # 全選當前內容
                    limit_price_input.send_keys(Keys.CONTROL, 'a')
                    time.sleep(0.1)

                    # 輸入新價格
                    limit_price_input.send_keys(str(price))
                    time.sleep(0.1)

                    # 按下 Enter 確認輸入
                    limit_price_input.send_keys(Keys.ENTER)
                    time.sleep(0.5)

                    self._update_status(f"已設定價格: {price}")
                except Exception as e:
                    return False, f"設定價格失敗: {str(e)}"

            # 步驟3: 執行下單
            return self._place_single_order(direction)

        except Exception as e:
            return False, f"完整下單流程失敗: {str(e)}"

    def _place_single_order(self, direction):
        """
        下單一個訂單 (僅點擊下單按鈕)

        Args:
            direction (str): 交易方向 (Buy/Sell)

        Returns:
            tuple: (bool, str) - (是否成功, 狀態消息)
        """
        try:
            # 根據direction判斷買入還是賣出
            button_text = direction  # 直接使用direction作為按鈕文字

            # 尋找下單按鈕
            order_button = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH,
                    f"//button[contains(., '{button_text}')]"))
            )

            if order_button:
                order_button.click()
                time.sleep(0.5)
                return True, f"已執行{button_text}下單"
            else:
                return False, f"找不到{button_text}按鈕"

        except Exception as e:
            return False, f"執行{direction}下單失敗: {str(e)}"